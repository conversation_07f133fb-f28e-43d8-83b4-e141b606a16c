<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 14:56:04
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-03 09:59:03
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Modal\ExtractorSet.vue
 * 
-->
<template>
  <div>
    <el-dialog title="运行设置" v-model="dialogueFlag" width="520px" append-to-body>
      <div style="padding: 30px">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="120px">
          <el-form-item label="自动速度" prop="speedValue">
            <el-input-number
              v-model="addForm.speedValue"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="手动速度" prop="manualSpeed">
            <el-input-number
              v-model="addForm.manualSpeed"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="加速度" prop="retardedSpeed">
            <el-input-number
              v-model="addForm.retardedSpeed"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="减速度" prop="deceleration">
            <el-input-number
              v-model="addForm.deceleration"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="定位窗口" prop="position">
            <el-input-number
              v-model="addForm.position"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { updateLiftDataApi } from "@/api/home/<USER>";
const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  speedValue: [{ required: true, message: "请输入", trigger: "blur" }],
  manualSpeed: [{ required: true, message: "请输入", trigger: "blur" }],
  retardedSpeed: [{ required: true, message: "请输入", trigger: "blur" }],
  deceleration: [{ required: true, message: "请输入", trigger: "blur" }],
  position: [{ required: true, message: "请输入", trigger: "blur" }],
});

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      requestObj = updateLiftDataApi(addForm.value);
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("操作成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
