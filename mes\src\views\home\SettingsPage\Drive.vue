<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 09:41:22
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 15:17:34
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Drive.vue
 * 
-->
<template>
  <div class="top">
    <div style="width: 200px">
      <el-tabs v-model="activeName" :stretch="true" tab-position="left">
        <el-tab-pane label="升降机" :name="1"></el-tab-pane>
        <el-tab-pane label="提取器" :name="2"></el-tab-pane>
        <el-tab-pane label="安全门" :name="3"></el-tab-pane>
      </el-tabs>
    </div>

    <Lift v-if="activeName == 1"></Lift>
    <Extractor v-if="activeName == 2"></Extractor>
    <Door v-if="activeName == 3"></Door>
  </div>

</template>

<script setup>
const Lift = defineAsyncComponent(() => import("./Drive/Lift.vue"));
const Extractor = defineAsyncComponent(() => import("./Drive/Extractor.vue"));
const Door = defineAsyncComponent(() => import("./Drive/Door.vue"));

let activeName = ref(1);
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  width: 1000px;
}

:deep(.com-border) {
  width: 400px;
  height: 230px;
  border: 1px solid #c1c1c1;

  .title {
    height: 40px;
    line-height: 40px;
    background-color: #fafafa;
    border-bottom: 1px solid #c1c1c1;
    padding-left: 20px;
    font-weight: bold;
  }

  .text {
    padding-left: 20px;

    .el-col {
      margin-top: 15px;
      margin-right: 10px;
    }
    .btn {
      margin-top: 30px;
      text-align: center;
    }
  }
}
</style>
