<template>
  <div>
    <el-dialog title="传感器(ILK)" v-model="dialogueFlag" width="520px" append-to-body>
      <el-form ref="ruleFormRef" :model="addForm" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="传感器A">
              <el-switch
                v-model="addForm.A"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="传感器B">
              <el-switch
                v-model="addForm.B"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="传感器C">
              <el-switch
                v-model="addForm.C"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="传感器D">
              <el-switch
                v-model="addForm.D"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="传感器E">
              <el-switch
                v-model="addForm.E"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="传感器F">
              <el-switch
                v-model="addForm.F"
                inline-prompt
                active-text="开"
                inactive-text="关"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({
  A:false,
  B:false,
});

// 初始化弹窗
function openModal() {
  dialogueFlag.value = true;
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      // if (addForm.value.lineId) {
      //   requestObj = editLinePageApi(addForm.value);
      // } else {
      //   requestObj = addLinePageApi(addForm.value);
      // }
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess('操作成功');
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
