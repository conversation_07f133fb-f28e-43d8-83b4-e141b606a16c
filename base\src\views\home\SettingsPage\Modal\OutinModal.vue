<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 13:32:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-03 11:28:56
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Modal\OutinModal.vue
 * 
-->
<template>
  <div>
    <el-dialog
      :title="addForm.cabinetGateId !== undefined ? '编辑出入口' : '新增出入口'"
      v-model="dialogueFlag"
      width="520px"
      append-to-body
    >
      <div style="padding: 30px">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="140px">
          <el-form-item label="货柜" prop="cabinetId">
            <el-select
              v-model="addForm.cabinetId"
              placeholder="请选择"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in HuoGuiList"
                :key="item.creatorId"
                :label="item.cabinetName"
                :value="item.cabinetId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="出入口" prop="gateCode">
            <el-input v-model="addForm.gateCode" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="方向" prop="gateDirection">
            <el-input-number
              v-model="addForm.gateDirection"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="支架高度(mm)" prop="gateRackHeight">
            <el-input-number
              v-model="addForm.gateRackHeight"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="偏移量(mm)" prop="gateOffset">
            <el-input-number
              v-model="addForm.gateOffset"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="高度(mm)" prop="gateHeight">
            <el-input-number
              v-model="addForm.gateHeight"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getHuoGuoApi } from "@/api/loginApi";
import { addOutInPageApi, editOutInPageApi } from "@/api/home/<USER>";
const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  cabinetId: [{ required: true, message: "请选择", trigger: "blur" }],
  gateCode: [{ required: true, message: "请输入", trigger: "blur" }],
  gateDirection: [{ required: true, message: "请输入", trigger: "blur" }],
  gateRackHeight: [{ required: true, message: "请输入", trigger: "blur" }],
  gateOffset: [{ required: true, message: "请输入", trigger: "blur" }],
  gateHeight: [{ required: true, message: "请输入", trigger: "blur" }],
});

//货柜
let HuoGuiList = ref([]);
const getHuoGuoApiFn = async () => {
  HuoGuiList.value = await getHuoGuoApi();
};

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  getHuoGuoApiFn();
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      if (addForm.value.cabinetGateId) {
        requestObj = editOutInPageApi({
          ...addForm.value,
          // cabinetId: localStorage.getItem("huogui.id"),
        });
      } else {
        requestObj = addOutInPageApi({
          ...addForm.value,
          // cabinetId: localStorage.getItem("huogui.id"),
        });
      }
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess(addForm.value.cabinetGateId ? "编辑成功" : "新增成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped>
.attentaion {
  padding: 0 20px;
  width: 100%;
  height: 140px;
  border: 1px solid #faad14;
  border-radius: 4px;
  background-color: #fffbe6;
  div {
    color: #8d8b7f;
  }
}
</style>
