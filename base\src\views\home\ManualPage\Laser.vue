<!--
 * @Author: 方志良 
 * @Date: 2025-06-30 10:23:44
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-30 14:35:55
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\ManualPage\Laser.vue
 * 
-->
<template>
  <div class="laser">
    <!-- <el-form-item label="出入口">
      <el-select
        v-model="addForm.cabinetGateId"
        placeholder="请选择"
        clearable
        filterable
      >
        <el-option
          v-for="item in OutInList"
          :key="item.cabinetGateId"
          :label="item.gateCode"
          :value="item.cabinetGateId"
        />
      </el-select>
    </el-form-item> -->

    <h4>水平位置</h4>
    <div class="top">
      <div>
        <img
          src="@/assets/images/left-off.png"
          alt="向左箭头-关"
          v-if="levelLeftFlag == false"
          @click="levelLeftMoveFn"
        />
        <img
          src="@/assets/images/left-on.png"
          alt="向左箭头-开"
          v-else
          @click="levelLeftMoveFn"
        />
      </div>
      <div class="poi">
        左
        <div class="center">
          <!-- <div class="line" :style="{ left: linePosition + 'px' }">
            <div class="line-son"></div>
          </div> -->
        </div>
        右
      </div>
      <div>
        <img
          src="@/assets/images/right-off.png"
          alt="向右箭头-关"
          v-if="levelRightFlag == false"
          @click="levelRightMoveFn"
        />
        <img
          src="@/assets/images/right-on.png"
          v-else
          alt="向右箭头-开"
          @click="levelRightMoveFn"
        />
      </div>
    </div>
    <div>位置: {{ currentPosition }}mm</div>

    <h4 style="margin-top: 100px">倾斜角度</h4>
    <div class="bottom">
      <div>
        <img
          src="@/assets/images/left-off.png"
          alt="向左箭头-关"
          v-if="tiltLeftFlag == false"
          @click="tiltLeftMoveFn"
        />
        <img
          src="@/assets/images/left-on.png"
          alt="向左箭头-开"
          v-else
          @click="tiltLeftMoveFn"
        />
      </div>
      <div class="poi">
        正面
        <div class="center"></div>
        后面
      </div>
      <div>
        <img
          src="@/assets/images/right-off.png"
          alt="向右箭头-关"
          v-if="tiltRightFlag == false"
          @click="tiltRightMoveFn"
        />
        <img
          src="@/assets/images/right-on.png"
          v-else
          alt="向右箭头-开"
          @click="tiltRightMoveFn"
        />
      </div>
    </div>
    <div>当前角度: {{ currentTiltPosition }}°</div>
  </div>
</template>

<script setup>
import { cabinetActionApi } from "@/api/home/<USER>";

import { useStore } from "vuex";
import useWebSocket from "@/utils/useWebSocket";
const store = useStore();

// import { getOutInApi } from "@/api/loginApi";
// const addForm = ref({});
//出入口
// let OutInList = ref([]);
// const getOutInApiFn = async (id) => {
//   // OutInList.value = await getOutInApi({
//   //   cabinetId: localStorage.getItem("huogui.id"),
//   // });
// };

// getOutInApiFn();


//获取基本信息
const connectWebsocketFn = (code) => {
  const { connect } = useWebSocket();

  const buildParams = (actionCode) => {
    return () => ({
      userId: store.state.user.userinfo.userId,
      cabinetId: localStorage.getItem("huogui.id"),
      gateCode: localStorage.getItem("outin.code"),
      type: 2,
      manualActionDataCommand: {
        deviceCode: 400,
        actionCode,
      },
    });
  };

  const handleMessage = (res) => {
    currentPosition.value=res.data.currentXPosition
    currentTiltPosition.value=res.data.currentZPosition
  };

  connect(import.meta.env.VITE_APP_WEBSOCKET, buildParams(code), handleMessage);
};

const { proxy } = getCurrentInstance();
const manualFlag = inject("manualFlag");

// 水平移动相关变量
const linePosition = ref(0); // 从左边开始的位置（px）
const currentPosition = ref(0); // 当前位置（mm）
const currentSpeed = ref(0); // 当前速度
const movingInterval = ref(null);
const totalWidth = 500; // 中心区域总宽度（px）
const moveSpeed = 44; // 每秒移动44px（10秒从左到右）

//向左移动
let levelLeftFlag = ref(false); //默认关
const levelLeftMoveFn = () => {
  if (!manualFlag.value) return proxy.$modal.msgError("请先开启手动模式");
  levelLeftFlag.value = levelLeftFlag.value == true ? false : true;
  stopTiltMoving();
  if (levelLeftFlag.value) {
    startMovingLeft();
  } else {
    stopMoving();
  }
};

//向右移动
let levelRightFlag = ref(false); //默认关
const levelRightMoveFn = () => {
  if (!manualFlag.value) return proxy.$modal.msgError("请先开启手动模式");
  levelRightFlag.value = levelRightFlag.value == true ? false : true;
  stopTiltMoving();
  if (levelRightFlag.value) {
    startMovingRight();
  } else {
    stopMoving();
  }
};

// 开始向左移动
const startMovingLeft = async () => {
  levelRightFlag.value = false;
  tiltLeftFlag.value = false;
  tiltRightFlag.value = false;

  await cabinetActionApi({
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: localStorage.getItem("outin.code"),
    deviceCode: 400,
    actionCode: 7,
  });

  connectWebsocketFn(7)
};

// 开始向右移动
const startMovingRight = async () => {
  levelLeftFlag.value = false;
  tiltLeftFlag.value = false;
  tiltRightFlag.value = false;


  await cabinetActionApi({
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: localStorage.getItem("outin.code"),
    deviceCode: 400,
    actionCode: 8,
  });

    connectWebsocketFn(8)
};

// 停止移动
const stopMoving = () => {
  clearInterval(movingInterval.value);
  movingInterval.value = null;
  currentSpeed.value = 0;
};

// 水平移动相关变量
const lineTiltPosition = ref(0); // 从左边开始的位置（px）
const currentTiltPosition = ref(0); // 当前位置（mm）
const currentTiltSpeed = ref(0); // 当前速度
const movingTiltInterval = ref(null);
const totaTiltlWidth = 500; // 中心区域总宽度（px）
const moveTiltSpeed = 44; // 每秒移动44px（10秒从左到右）

//向左移动
let tiltLeftFlag = ref(false); //默认关
const tiltLeftMoveFn = () => {
  if (!manualFlag.value) return proxy.$modal.msgError("请先开启手动模式");
  tiltLeftFlag.value = tiltLeftFlag.value == true ? false : true;
  stopMoving();
  if (tiltLeftFlag.value) {
    startTiltMovingLeft();
  } else {
    stopTiltMoving();
  }
};

//向右移动
let tiltRightFlag = ref(false); //默认关
const tiltRightMoveFn = () => {
  if (!manualFlag.value) return proxy.$modal.msgError("请先开启手动模式");
  tiltRightFlag.value = tiltRightFlag.value == true ? false : true;
  stopMoving();
  if (tiltRightFlag.value) {
    startTiltMovingRight();
  } else {
    stopTiltMoving();
  }
};

// 开始向左移动
const startTiltMovingLeft = async () => {
  levelLeftFlag.value = false;
  levelRightFlag.value = false;
  tiltRightFlag.value = false;


  await cabinetActionApi({
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: localStorage.getItem("outin.code"),
    deviceCode: 400,
    actionCode: 9,
  });

    connectWebsocketFn(9)
};

// 开始向右移动
const startTiltMovingRight = async () => {
  levelLeftFlag.value = false;
  levelRightFlag.value = false;
  tiltLeftFlag.value = false;

  await cabinetActionApi({
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: localStorage.getItem("outin.code"),
    deviceCode: 400,
    actionCode: 10,
  });

    connectWebsocketFn(10)
};

// 停止移动
const stopTiltMoving = () => {
  clearInterval(movingTiltInterval.value);
  movingTiltInterval.value = null;
  currentTiltSpeed.value = 0;
};

onMounted(() => {
  connectWebsocketFn(7);
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (movingInterval.value) clearInterval(movingInterval.value);
  if (movingTiltInterval.value) clearInterval(movingTiltInterval.value);
});
</script>

<style lang="scss" scoped>
.laser {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  h4 {
    font-weight: 700;
  }

  img {
    width: 55px;
    height: 75px;
    cursor: pointer;
  }

  .poi {
    display: flex;
    align-items: center;
    height: 75px;
  }
  .center {
    position: relative;
    width: 500px;
    height: 10px;
    background-color: #e6e6e6;
    margin: 0 10px;

    .line {
      position: absolute;
      left: 0px;
      bottom: 10px;
      width: 60px;
      height: 10px;
      background-color: #162437;

      .line-son {
        position: absolute;
        left: 15px;
        bottom: 10px;
        width: 30px;
        height: 10px;
        background-color: #162437;
      }
    }
  }
  .top,
  .bottom {
    display: flex;
  }

  .top .poi {
    margin: 0 30px;
  }
  .bottom .poi {
    margin: 0 15px;
  }
}
</style>
