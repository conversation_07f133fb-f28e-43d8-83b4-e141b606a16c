<template>
  <div class="keypad-container">
    <div class="keypad-grid">
      <button
        v-for="num in 9"
        :key="num"
        class="key-button"
        @click="handleNumberClick(num)"
      >
        {{ num }}
      </button>
      <button class="key-button" @click="handleNumberClick(0)">0</button>
      <button class="key-button delete-key" @click="handleDelete">
        <svg class="delete-icon" viewBox="0 0 24 24">
          <path
            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
import { defineEmits } from "vue";

const emit = defineEmits(["input", "delete"]);

const handleNumberClick = (number) => {
  emit("input", number);
};

const handleDelete = () => {
  emit("delete");
};
</script>

<style scoped>
.keypad-container {
  width: 100%;
  max-width: 500px;
}

.keypad-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.key-button {
  background-color: #4a5568;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 20px;
  font-size: 28px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s;
}

.key-button:hover {
  background-color: #2d3748;
}

.delete-key {
  background-color: #4a5568;
}

.delete-icon {
  width: 24px;
  height: 24px;
  fill: white;
}
</style>
