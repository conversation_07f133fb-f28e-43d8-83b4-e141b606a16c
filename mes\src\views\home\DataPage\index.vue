<!--
 * @Author: 方志良 
 * @Date: 2025-06-26 17:07:42
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 10:05:05
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\DataPage\index.vue
 * 
-->
<template>
  <div class="app-container">
    <el-tabs v-model="activeName" :stretch="true" type="card">
      <el-tab-pane label="仓库" :name="1"></el-tab-pane>
      <el-tab-pane label="计数" :name="2"></el-tab-pane>
      <el-tab-pane label="通讯" :name="3"></el-tab-pane>
      <el-tab-pane label="维护" :name="4"></el-tab-pane>
    </el-tabs>

    <div style="display: flex; justify-content: center">
      <Ware v-if="activeName == 1"></Ware>
      <Count v-if="activeName == 2"></Count>
      <Communcate v-if="activeName == 3"></Communcate>
      <Maintain v-if="activeName == 4"></Maintain>
    </div>
  </div>
</template>

<script setup>
const Communcate = defineAsyncComponent(() => import("./Communcate.vue"));
const Count = defineAsyncComponent(() => import("./Count.vue"));
const Maintain = defineAsyncComponent(() => import("./Maintain.vue"));
const Ware = defineAsyncComponent(() => import("./Ware.vue"));

let activeName = ref(1);

onMounted(() => {});


</script>

<style lang="scss" scoped>
.el-tabs {
  width: 40%;
  margin: 0 auto;
}
</style>
