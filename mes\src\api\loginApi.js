/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-26 13:58:35
 * @FilePath: \aosikai-cabinet-wms-web\base\src\api\loginApi.js
 * 
 */

import request from '@/utils/request';

// 登录方法
export function login(username, password) {
  const data = {
    username,
    password
  };
  return request({
    url: '/login',
    method: 'post',
    data,
  });
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false,
    },
    method: 'post',
    data,
  });
}

// 获取用户详细信息
export function getLoginUserInfo() {
  return request({
    url: '/getLoginUserInfo',
    method: 'get',
  });
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post',
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false,
    },
    method: 'get',
    timeout: 20000,
  });
}

// 获取路由
export const getRouters = () =>
  request({
    url: '/getRouters',
    method: 'get',
  });

// 获取货柜列表
export const getHuoGuoApi = params =>
  request({
    url: `/cabinet`,
    method: 'get',
    headers: {
      isToken: false,
    },
    params
  });

// 获取出入口列表
export const getOutInApi = params =>
  request({
    url: `/cabinet/gate`,
    method: 'get',
    headers: {
      isToken: false,
    },
    params
  });