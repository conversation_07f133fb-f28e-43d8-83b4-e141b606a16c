<!--
 * @Author: 方志良 
 * @Date: 2024-04-02 17:10:14
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-05-07 17:37:53
 * @FilePath: \biweiman-mes-wms-front-end\src\views\system\role\roleLeft.vue
 * 
-->
<template>
  <el-card class="left">
    <SearchList v-model="keywords" style="width: 100%;margin-left: 1px;" @update:modelValue="theListFn"></SearchList>
    <el-button type="primary" class="left-btn" v-hasPermission="['system:role:add']"
      @click="dialogFlag = true; editObj = {}">添加角色</el-button>
    <el-menu mode="vertical" @select="selectBtn">
      <el-menu-item :index="`${item.roleId}`" v-for="item in tableList" :key="item.roleId" class="menu">
        <span>{{ item.roleName }}</span>
        <div class="btn-group" v-show="showButtons(item)">
          <el-button class="menu-btn" text v-hasPermission="['system:role:edit']" @click="editListtn(item)">编辑</el-button>
          <el-button class="menu-btn" text v-hasPermission="['system:role:remove']"
            @click="deleteListBtn(item)">删除</el-button>
        </div>
      </el-menu-item>
    </el-menu>
  </el-card>

  <RoleAddOrEdit v-model:isvisable="dialogFlag" :editObj="editObj" @onSuccess="theListFn"></RoleAddOrEdit>
</template>

<script setup>
import { listRole, deleteRoleApi } from '@/api/system/roleApi'
const RoleAddOrEdit = defineAsyncComponent(() => import('./component/RoleAddOrEdit'))
const { proxy } = getCurrentInstance();
const activeIndex = ref('')
const keywords = ref("")
const tableList = ref([])
const theListFn = async () => {
  let obj = {
    keywords: keywords.value
  }
  tableList.value = await listRole(obj)
}
//选中
const emit = defineEmits(['onSuccess']);
const selectBtn = (e, item) => {
  activeIndex.value = e
  emit('onSuccess', e);
}
const showButtons = (item) => {
  return activeIndex.value == item.roleId ? true : false
}

//弹框新增编辑
let dialogFlag = ref(false)
let editObj = ref({})
const editListtn = (row) => {
  editObj.value = row
  dialogFlag.value = true
}

//删除
const deleteListBtn = (row) => {
  proxy.$modal
    .confirm(`是否确认删除${row.roleName}角色？`)
    .then(() => deleteRoleApi({ ids: [row.roleId] }))
    .then(() => {
      proxy.$modal.msgSuccess(`删除成功`);
      theListFn()
      emit('onSuccess', "");
    })
    .catch(() => {
    });
}

onMounted(() => {
  theListFn()
})
</script>

<style lang="scss" scoped></style>