<!--
 * @Author: 方志良 
 * @Date: 2025-06-26 17:07:42
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 09:38:47
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\ManualPage\index.vue
 * 
-->
<template>
  <div class="app-container">
    <div style="display: flex">
      <div>
        <el-button @click="handleAddEdit" v-if="manualFlag === true">
          传感器(ILK)
        </el-button>
      </div>
      <el-tabs v-model="activeName" :stretch="true" type="card">
        <el-tab-pane label="升降机" :name="1"></el-tab-pane>
        <el-tab-pane label="提取器" :name="2"></el-tab-pane>
        <el-tab-pane label="安全门" :name="3"></el-tab-pane>
        <el-tab-pane label="激光指示器" :name="4"></el-tab-pane>
      </el-tabs>
      <el-form-item label="手动模式:">
        <el-switch
          v-model="manualFlag"
          @change="changeFn"
          inline-prompt
          active-text="开"
          inactive-text="关"
        />
      </el-form-item>
    </div>

    <div style="display: flex; justify-content: center" v-if="manualFlag">
      <Lift v-if="activeName == 1"></Lift>
      <Extractor v-if="activeName == 2"></Extractor>
      <Door v-if="activeName == 3"></Door>
      <Laser v-if="activeName == 4"></Laser>
    </div>

    <ILK ref="ILKRef" />
  </div>
</template>

<script setup>
import { cabinetModeApi } from "@/api/home/<USER>";

const Lift = defineAsyncComponent(() => import("./Lift.vue"));
const Door = defineAsyncComponent(() => import("./Door.vue"));
const Extractor = defineAsyncComponent(() => import("./Extractor.vue"));
const Laser = defineAsyncComponent(() => import("./Laser.vue"));
const ILK = defineAsyncComponent(() => import("./modal/ILK.vue"));
let activeName = ref(1);
let manualFlag = ref(false);
provide("manualFlag", manualFlag);
const { proxy } = getCurrentInstance();
const cabinetModeApiFn = async (mode) => {
  await cabinetModeApi({
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: localStorage.getItem("outin.code"),
    mode,
  });
  proxy.$modal.msgSuccess(`手动模式${mode == 2 ? "开启" : "关闭"}成功`);
};

const changeFn = (val) => {
  if (val == true) {
    cabinetModeApiFn(2);
  } else {
    cabinetModeApiFn(1);
  }
};

onMounted(() => {});

//传感器
const ILKRef = ref(null);
const handleAddEdit = async (row) => {
  ILKRef.value.openModal(row);
};

onUnmounted(() => {
  cabinetModeApiFn(1);
});
</script>

<style lang="scss" scoped>
.el-tabs {
  width: 40%;
  margin: 0 auto;
}
</style>
