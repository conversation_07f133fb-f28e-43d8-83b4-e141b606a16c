<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 14:56:04
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-03 10:11:00
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Modal\HuoGuiModal.vue
 * 
-->
<template>
  <div>
    <el-dialog title="货柜" v-model="dialogueFlag" width="520px" append-to-body>
      <div style="padding: 30px">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="120px">
          <el-form-item label="名称" prop="cabinetName">
           <el-input v-model="addForm.cabinetName" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="区域" prop="area">
            <el-input v-model="addForm.area" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="编号" prop="code">
             <el-input v-model="addForm.code" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="货柜高度" prop="height">
            <el-input-number
              v-model="addForm.height"
              controls-trayWidth="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="托盘宽度" prop="trayWidth">
            <el-input-number
              v-model="addForm.trayWidth"
              controls-trayWidth="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="托盘长度" prop="trayLength">
            <el-input-number
              v-model="addForm.trayLength"
              controls-trayWidth="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

       
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { updateHuoGuiDataApi } from "@/api/home/<USER>";
const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  cabinetName: [{ required: true, message: "请输入", trigger: "blur" }],
  area: [{ required: true, message: "请输入", trigger: "blur" }],
  code: [{ required: true, message: "请输入", trigger: "blur" }],
  height: [{ required: true, message: "请输入", trigger: "blur" }],
  trayWidth: [{ required: true, message: "请输入", trigger: "blur" }],
  trayLength: [{ required: true, message: "请输入", trigger: "blur" }],
});

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      requestObj = updateHuoGuiDataApi(addForm.value);
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("操作成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
