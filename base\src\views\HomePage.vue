<!--
 * @Author: 方志良 
 * @Date: 2025-06-26 13:27:19
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-30 10:04:55
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\HomePage.vue
 * 
-->
<template>
  <div class="home">
    <TopHeader :TopTitle="currentPageTitle"></TopHeader>

    <section class="home-main">
      <router-view v-slot="{ Component, route }">
        <transition name="fade-transform" mode="out-in">
          <keep-alive :include="cachedViews">
            <component :is="Component" :key="route.path" />
          </keep-alive>
        </transition>
      </router-view>
    </section>

    <div class="home-menu">
      <div
        v-for="menu in menuList"
        :key="menu.path"
        :class="{ active: isActive(menu.path) }"
        v-hasPermission="[menu.hasPermission]"
        @click="navigateTo(menu.path)"
        class="menu-item"
      >
        <img class="menu-icon" :src="menu.icon" alt="" />
        <div class="menu-title">{{ menu.title }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import mainImg from "@/assets/images/up.svg";

import alarmImg from "@/assets/images/alarm.svg";
import manualImg from "@/assets/images/manual.svg";
import dataImg from "@/assets/images/data.svg";
import trayImg from "@/assets/images/tray.svg";
import settingsImg from "@/assets/images/settings.svg";

const router = useRouter();
const route = useRoute();

const cachedViews = ref([]);

// 菜单配置
const menuList = ref([
  {
    path: "/HomePage/main",
    title: "主页面",
    // icon: "🏠",
    icon: mainImg,
    hasPermission: "",
  },
  {
    path: "/HomePage/alarm",
    title: "设备报警",
    // icon: "🚨",
    icon: alarmImg,
    hasPermission: "HomePage:alarm:index",
  },
  {
    path: "/HomePage/manual",
    title: "手动模式",
    // icon: "🛠️",
    icon: manualImg,
    hasPermission: "HomePage:manual:index",
  },
  {
    path: "/HomePage/data",
    title: "设备数据",
    // icon: "📊",
    icon: dataImg,
    hasPermission: "HomePage:data:index",
  },
  {
    path: "/HomePage/tray",
    title: "托盘管理",
    // icon: "📦",
    icon: trayImg,
    hasPermission: "HomePage:tray:index",
  },
  {
    path: "/HomePage/settings",
    title: "设置",
    // icon: "⚙️",
    icon: settingsImg,
    hasPermission: "HomePage:settings:index",
  },
  {
    path: "/index",
    title: "后台管理",

    // icon: "👤",
    icon: mainImg,
    hasPermission: "HomePage:backing:index",
  },
]);

// 当前页面标题
const currentPageTitle = computed(() => {
  const currentMenu = menuList.value.find((menu) => menu.path === route.path);
  return currentMenu ? currentMenu.title : "首页";
});

// 判断菜单是否激活
const isActive = (path) => {
  return route.path === path;
};

// 导航到指定路由
const navigateTo = (path) => {
  if (route.path !== path) {
    router.push(path);
  }
};

// 监听路由变化，更新缓存视图
watch(
  () => route.name,
  (newName) => {
    if (newName && !cachedViews.value.includes(newName)) {
      cachedViews.value.push(newName);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.home {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
}

.home-main {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - 80px); /* 减去底部菜单高度 */
}

.home-menu {
  display: flex;
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 80px;
  background-color: #dff1fa;
  border-top: 1px solid #ebebeb;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  gap: 10px;
  padding: 0 20px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background-color: transparent;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f5f5f5;
    color: #409eff;
  }

  &.active {
    background-color: #409eff;
    color: #ffffff;

    &:hover {
      background-color: #337ecc;
    }
  }
}

.menu-icon {
  font-size: 20px;
  margin-bottom: 4px;
  width: 46px;
  height: 35px;
}

.menu-title {
  font-size: 12px;
  text-align: center;
  line-height: 1;
}

// 添加页面过渡动画
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
