<!--
 * @Author: 方志良 
 * @Date: 2025-06-26 17:06:55
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 16:47:54
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\MainPage\index.vue
 * 
-->
<template>
  <div class="container">
    <div class="baseinfo">
      <div class="info">
        <div class="info-left">
          <div style="margin-bottom: 10px">重量:0-300kg</div>
          <div>高度:194mm</div>
        </div>
        <div class="info-right">
          <div class="info-right-flex">
            <div style="margin-bottom: 10px">当前出库:</div>
            <div style="color: #12ff46; font-size: 26px">#1</div>
          </div>
          <div class="info-right-flex">
            <div style="margin-bottom: 10px">待出库数量:</div>
            <div style="color: #11eefe; font-size: 26px">3</div>
          </div>
        </div>
      </div>
      <img src="@/assets/images/inware.jpg" alt="入库" />
    </div>
    <div class="calc">
      <el-input v-model="displayValue" size="large" placeholder="请输入">
        <template #append>
          <el-button>
            <el-icon :size="25">
              <Search />
            </el-icon>
          </el-button>
        </template>
      </el-input>

      <div class="tool">
        <div class="keyboard">
          <Calauate @input="onInput" @delete="onDelete"></Calauate>
        </div>

        <div class="tool-right">
          <div class="out-btn" @click="outBtn">出 库</div>
          <div class="in-btn" @click="inBtn">入 库</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { inWareTaskApi, outWareTaskApi } from "@/api/home/<USER>";
import { Search } from "@element-plus/icons-vue";
const Calauate = defineAsyncComponent(() => import("@/views/component/Calauate.vue"));
const { proxy } = getCurrentInstance();
let displayValue = ref("");
const onInput = (number) => {
  displayValue.value += number;
};

const onDelete = () => {
  displayValue.value = displayValue.value.slice(0, -1);
};

//出库
const outBtn = async () => {
  await outWareTaskApi({
    trayCode: displayValue.value,
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: localStorage.getItem("outin.code"),
  });
  proxy.$modal.msgSuccess("出库成功");
};

//入库
const inBtn = async () => {
  await inWareTaskApi({
    trayCode: displayValue.value,
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: localStorage.getItem("outin.code"),
  });
  proxy.$modal.msgSuccess("入库成功");
};
</script>

<style lang="scss" scoped>
.container {
  background-color: #162437;
  min-height: 100%;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;

  .baseinfo {
    width: 500px;
    height: 550px;

    .info {
      display: flex;

      &-left {
        width: 200px;
        height: 100px;
        background-color: #273c5b;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 30px;

        div {
          font-size: 18px;
        }
      }

      &-right {
        flex: 1;
        margin-left: 20px;
        height: 100px;
        background-color: #273c5b;
        border-radius: 10px;
        display: flex;

        &-flex {
          flex: 1;

          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }
      }
    }
    img {
      width: 100%;
      margin-top: 30px;
      height: 420px;
    }
  }

  .calc {
    width: 500px;
    height: 550px;
    margin-left: 20px;
    background-color: #243a5a;
    border-radius: 20px;
    padding: 20px;

    .tool {
      margin-top: 30px;
      display: flex;

      .keyboard {
        width: 75%;
      }

      &-right {
        margin-left: 30px;
        flex: 1;
        .out-btn,
        .in-btn {
          width: 100%;
          height: 150px;
          line-height: 150px;
          background-color: #509bf7;
          border-radius: 8px;
          font-size: 20px;
          text-align: center;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .out-btn {
          background-color: #509bf7;
        }
        .in-btn {
          margin-top: 20px;
          background-color: #07c0b9;
        }
      }
    }
  }
}

:deep(.el-input-group__append) {
  background-color: #4aa8ff;
  color: #ffffff;
  width: 80px;
  display: flex;
  align-items: center;
}
:deep(.el-input) {
  height: 50px;
  line-height: 50px;
}
:deep(.el-icon) {
  margin-bottom: 20px;
}
</style>
