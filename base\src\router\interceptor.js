/*
 * @Author: 方志良 
 * @Date: 2024-03-29 17:18:13
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-27 11:22:45
 * @FilePath: \aosikai-cabinet-wms-web\base\src\router\interceptor.js
 * 
 */
import { ElMessage } from 'element-plus';
import NProgress from 'nprogress';
import router from '.';
import store from '../store';
import 'nprogress/nprogress.css';
import { getToken } from '@/utils/token';
import { isHttp } from '@/utils/validate';
import { isReLogin } from '@/utils/request';

NProgress.configure({ showSpinner: false });
const whiteList = ['/login', '/register', '/screen', '/ExitPage', '/401'];
router.beforeEach((to, from, next) => {
  NProgress.start();
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title);

    /* has token */
    if (to.path === '/login') {
      next({ path: '/' });
      NProgress.done();
    } else if (!store.getters.role ) {
      isReLogin.show = true;
      // 判断当前用户是否已拉取完user_info信息
      store
        .dispatch('GetInfo')
        .then(() => {
          isReLogin.show = false;
          store.dispatch('GenerateRoutes').then((accessRoutes) => {
            // 根据roles权限生成可访问的路由表
            accessRoutes.forEach((route) => {
              if (!isHttp(route.path)) {
                router.addRoute(route); // 动态添加可访问路由表
              }
            });
            next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
          });
        })
        .catch((err) => {
          store.dispatch('LogOut').then(() => {
            ElMessage.error(err);
            next({ path: '/' });
          });
        });
    } else {
      next();
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      // next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      next(`/ExitPage`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
