<!--
 * @Author: 方志良 
 * @Date: 2025-06-30 10:23:44
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-30 11:42:16
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\ManualPage\Door.vue
 * 
-->
<template>
  <div>
    <!-- <el-form-item label="出入口">
      <el-select
        v-model="addForm.cabinetGateId"
        placeholder="请选择"
        clearable
        filterable
      >
        <el-option
          v-for="item in OutInList"
          :key="item.cabinetGateId"
          :label="item.gateCode"
          :value="item.cabinetGateId"
        />
      </el-select>
    </el-form-item> -->

    <div class="status">
      当前状态:
      <div class="btn" v-if="doorFlag == false" @click="isDoorBtn('on')">开</div>
      <div class="btn" v-else style="background-color: orange" @click="isDoorBtn('off')">
        关
      </div>
    </div>
  </div>
</template>

<script setup>
import { cabinetActionApi } from "@/api/home/<USER>";

import { useStore } from "vuex";
import useWebSocket from "@/utils/useWebSocket";
const store = useStore();

// import { getOutInApi } from "@/api/loginApi";
// const addForm = ref({});
//出入口
// let OutInList = ref([]);
// const getOutInApiFn = async (id) => {
//   // OutInList.value = await getOutInApi({
//   //   cabinetId: localStorage.getItem("huogui.id"),
//   // });
// };

// getOutInApiFn();

//获取基本信息
const connectWebsocketFn = (code) => {
  const { connect } = useWebSocket();

  const buildParams = (actionCode) => {
    return () => ({
      userId: store.state.user.userinfo.userId,
      cabinetId: localStorage.getItem("huogui.id"),
      gateCode: localStorage.getItem("outin.code"),
      type: 2,
      manualActionDataCommand: {
        deviceCode: 300,
        actionCode,
      },
    });
  };

  const handleMessage = (res) => {

  };

  connect(import.meta.env.VITE_APP_WEBSOCKET, buildParams(code), handleMessage);
};

const { proxy } = getCurrentInstance();
const manualFlag = inject("manualFlag");

let doorFlag = ref(false);
const isDoorBtn = async (str) => {
  if (!manualFlag.value) return proxy.$modal.msgError("请先开启手动模式");
  if (str == "on") {
    await cabinetActionApi({
      cabinetId: localStorage.getItem("huogui.id"),
      gateCode: localStorage.getItem("outin.code"),
      deviceCode: 300,
      actionCode: 5,
    });
    doorFlag.value = true;
    connectWebsocketFn(5);
  } else {
    await cabinetActionApi({
      cabinetId: localStorage.getItem("huogui.id"),
      gateCode: localStorage.getItem("outin.code"),
      deviceCode: 300,
      actionCode: 6,
    });
    doorFlag.value = false;
    connectWebsocketFn(6);
  }
};
</script>

<style lang="scss" scoped>
.status {
  margin-top: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 20px;
    width: 80px;
    height: 80px;
    background-color: #7f7f7f;
    color: #ffffff;
    font-size: 25px;
    font-weight: 700;
  }
}
</style>
