<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 09:41:22
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 11:04:41
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\DataPage\Maintain.vue
 * 
-->
<template>
  <div>
    <div class="top">
      <div class="com-border">
        <div class="title">维护</div>
        <div class="text">
          <el-form ref="ruleFormRef1" :model="addForm" :rules="rules" label-width="100px">
            <el-form-item label="设备" prop="captureProjectName">
              <el-select
                v-model="addForm.captureProjectName"
                placeholder="请选择"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option label="货柜" value="货柜" />
                <el-option label="升降机" value="升降机" />
              </el-select>
            </el-form-item>
            <el-form-item label="类型">
              <el-select
                v-model="addForm.type"
                placeholder="请选择"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option label="维护" value="维护" />
                <el-option label="维修" value="维修" />
              </el-select>
            </el-form-item>
            <el-form-item label="操作人">
              <el-input v-model="addForm.captureProjectMainCode" placeholder="请输入" />
            </el-form-item>
          </el-form>

          <div class="btn">
            <el-button type="primary" @click="submitForm1" :loading="confirmLoading1"
              >保 存</el-button
            >
          </div>
        </div>
      </div>

      <div class="com-border" style="margin-top: 20px">
        <div class="title">设置</div>
        <div class="text">
          <el-form ref="ruleFormRef2" :model="addForm2" :rules="rules2" label-width="130px">
            <el-form-item label="维护周期(天)" prop="postSort">
              <el-input-number
                v-model="addForm2.postSort"
                controls-position="right"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-form>

          <div class="btn">
            <el-button type="primary" @click="submitForm2" :loading="confirmLoading2"
              >保 存</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const addForm = ref({});
const rules = reactive({
  captureProjectName: [{ required: true, message: "请输入", trigger: "blur" }],
});

//保存维护
const ruleFormRef1 = ref(null);
let confirmLoading1 = ref(false);
const submitForm1 = () => {
  ruleFormRef1.value.validate((valid) => {
    if (valid) {
      confirmLoading1.value = true;
      let requestObj = null;

      //   requestObj = addCaijiPageApi(addForm.value);

      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("保存成功");
          dialogueFlag.value = false;
        })
        .finally(() => {
          confirmLoading1.value = false;
        });
    }
  });
};

const addForm2=ref({})
const rules2 = reactive({
  postSort: [{ required: true, message: "请输入", trigger: "blur" }],
});

//保存设置
const ruleFormRef2 = ref(null);
let confirmLoading2 = ref(false);
const submitForm2 = () => {
  ruleFormRef2.value.validate((valid) => {
    if (valid) {
      confirmLoading2.value = true;
      let requestObj = null;

      //   requestObj = addCaijiPageApi(addForm.value);

      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("保存成功");
          dialogueFlag.value = false;
        })
        .finally(() => {
          confirmLoading2.value = false;
        });
    }
  });
};
</script>

<style lang="scss" scoped>
.top {
  .com-border {
    width: 600px;
    height: 300px;
    border: 1px solid #c1c1c1;

    .title {
      height: 40px;
      line-height: 40px;
      background-color: #fafafa;
      border-bottom: 1px solid #c1c1c1;
      padding-left: 20px;
      font-weight: bold;
    }

    .text {
      padding: 20px;

      .btn {
        text-align: center;
      }
    }
  }
}
</style>
