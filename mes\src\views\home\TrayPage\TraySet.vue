<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 11:24:48
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 14:09:38
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\TrayPage\TraySet.vue
 * 
-->
<template>
  <div class="tuopan">
    <img src="@/assets/images/tuopan.jpg" alt="托盘" class="img" />

    <div class="bottom">
      <div>长度: {{ trayObj.length }}mm</div>
      <div>宽度: {{ trayObj.width }} mm</div>
      <div>高度: {{ trayObj.height }}mm</div>
      <el-button type="primary" @click="setBtn">设 置</el-button>
    </div>

    <SetModal ref="SetModalRef" @on-success="getTrayTypeDetailApiFn" />
  </div>
</template>

<script setup>
import { getTrayTypeDetailApi } from "@/api/home/<USER>";

const SetModal = defineAsyncComponent(() => import("./modal/SetModal.vue"));

//传感器
const SetModalRef = ref(null);
const setBtn = () => {
  SetModalRef.value.openModal(trayObj.value);
};

let trayObj = ref({});
const getTrayTypeDetailApiFn = async () => {
  trayObj.value = await getTrayTypeDetailApi(1);
};

onMounted(() => {
  getTrayTypeDetailApiFn();
});
</script>

<style lang="scss" scoped>
.tuopan {
  margin-top: 20px;

  .img {
    width: 600px;
    height: 250px;
  }

  .bottom {
    margin-left: 250px;
    margin-top: 50px;

    div {
      margin-bottom: 20px;
      font-size: 20px;
      color: #5a5a5a;
    }
  }
}
</style>
