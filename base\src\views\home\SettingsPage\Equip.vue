<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 09:41:22
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 15:02:12
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Equip.vue
 * 
-->
<template>
  <div class="top">
    <div class="com-border">
      <div class="title">设备信息</div>
      <div class="text">
        <el-row>
          <el-col :span="11" style="text-align: right">客户名称: </el-col>
          <el-col :span="11">华为</el-col>
          <el-col :span="11" style="text-align: right">设备型号: </el-col>
          <el-col :span="11">华为</el-col>
          <el-col :span="11" style="text-align: right">序列号: </el-col>
          <el-col :span="11">华为</el-col>

          <el-col :span="11" style="text-align: right">最大载重: </el-col>
          <el-col :span="11">华为</el-col>
          <el-col :span="11" style="text-align: right">输入电压: </el-col>
          <el-col :span="11">华为</el-col>
          <el-col :span="11" style="text-align: right">输入电流: </el-col>
          <el-col :span="11">华为</el-col>

          <el-col :span="11" style="text-align: right">功率: </el-col>
          <el-col :span="11">华为</el-col>
        </el-row>

        <div class="btn">
          <el-button type="primary" @click="setBtn">设 置</el-button>
        </div>
      </div>
    </div>

    <EeuipModal ref="modalRef" />
  </div>
</template>

<script setup>
const EeuipModal = defineAsyncComponent(() => import("./Modal/EeuipModal.vue"));

const modalRef = ref(null);
const setBtn = () => {
  modalRef.value.openModal({});
};
</script>

<style lang="scss" scoped>
.top {
  .com-border {
    width: 400px;
    height: 400px;
    border: 1px solid #c1c1c1;

    .title {
      height: 40px;
      line-height: 40px;
      background-color: #fafafa;
      border-bottom: 1px solid #c1c1c1;
      padding-left: 20px;
      font-weight: bold;
    }

    .text {
      padding-left: 20px;

      .el-col {
        margin-top: 15px;
        margin-right: 10px;
      }
      .btn {
        margin-top: 30px;
        text-align: center;
      }
    }
  }
}
</style>
