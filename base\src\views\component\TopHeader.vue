<template>
  <div class="header-top">
    <div class="left">
      <div class="logo"></div>
      <h2>{{ TopTitle }}</h2>

      <div style="margin: 0 30px 0 80px; color: #7f7f7f">货柜:{{ huoguiTitle }}</div>
      <div style="color: #7f7f7f">出入口:{{ outInTitle }}</div>
    </div>

    <div class="center">{{ timer }}</div>

    <div class="right">
      <el-dropdown
        @command="handleCommand"
        class="right-menu-item hover-effect"
        trigger="click"
        v-if="showLogout"
      >
        <div class="avatar-wrapper">
          <img :src="getters.avatar" class="user-avatar" />
          <el-icon><caret-bottom /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="logout">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { dateFormate } from "@/utils/index";
import { onMounted } from "vue";
import { ElMessageBox } from "element-plus";
let props = defineProps({
  TopTitle: {
    type: String,
    default: "",
  },
  showLogout: {
    type: Boolean,
    default: true,
  },
});

const huoguiTitle = computed(() => localStorage.getItem("huogui.name") || "");
const outInTitle = computed(() => localStorage.getItem("outin.code") || "");

let timer = ref("");
onMounted(() => {
  timer.value = dateFormate();

  setInterval(() => {
    timer.value = dateFormate();
  }, 1000);
});

onBeforeUnmount(() => {
  clearInterval(timer.value);
});

const store = useStore();
const getters = computed(() => store.getters);
function handleCommand(command) {
  switch (command) {
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      store.dispatch("LogOut").then(() => {
        location.href = "/index";
      });
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped>
.header-top {
  height: 50px;
  background-color: #dff1fa;
  display: flex;
  align-items: center;

  .left {
    flex: 3;
    display: flex;
    align-items: center;
    height: 100%;

    .logo {
      width: 100px;
      height: 100%;
      background: url("../../assets/logo/logo.png");
      background-size: 80% 70%;
      background-position: center;
      background-repeat: no-repeat;
      margin: 0 20px;
    }
  }

  .center {
    flex: 2;
  }

  .right {
    flex: 1;
    display: flex;
    justify-content: end;
    margin-right: 20px;
  }
}
</style>
