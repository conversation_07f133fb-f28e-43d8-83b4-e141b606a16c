<!--
 * @Author: 方志良 
 * @Date: 2025-06-26 17:07:42
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 14:32:54
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\index.vue
 * 
-->
<template>
  <div class="app-container">
    <el-tabs v-model="activeName" :stretch="true" type="card">
      <el-tab-pane label="设备信息" :name="1"></el-tab-pane>
      <el-tab-pane label="驱动器" :name="2"></el-tab-pane>
      <el-tab-pane label="出入口" :name="3"></el-tab-pane>
      <el-tab-pane label="货柜" :name="4"></el-tab-pane>
    </el-tabs>

    <div style="display: flex; justify-content: center">
      <Equip v-if="activeName == 1"></Equip>
      <Drive v-if="activeName == 2"></Drive>
      <Outin v-if="activeName == 3"></Outin>
      <Cabinet v-if="activeName == 4"></Cabinet>
    </div>
  </div>
</template>

<script setup>
const Outin = defineAsyncComponent(() => import("./Outin.vue"));
const Drive = defineAsyncComponent(() => import("./Drive.vue"));
const Cabinet = defineAsyncComponent(() => import("./Cabinet.vue"));
const Equip = defineAsyncComponent(() => import("./Equip.vue"));

let activeName = ref(1);

onMounted(() => {});

</script>

<style lang="scss" scoped>
.el-tabs {
  width: 40%;
  margin: 0 auto;
}
</style>
