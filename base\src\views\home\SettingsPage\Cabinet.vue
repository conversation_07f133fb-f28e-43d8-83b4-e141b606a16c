<!--
 * @Author: 方志良 
 * @Date: 2025-07-03 09:26:05
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-03 10:06:47
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Cabinet.vue
 * 
-->
<template>
  <div>
    <div class="com-border" style="height: 400px">
      <div class="title">货柜</div>
      <div class="text">
        <el-row>
          <el-col :span="11" style="text-align: right">名称: </el-col>
          <el-col :span="11">{{ setObj.cabinetName }}</el-col>
          <el-col :span="11" style="text-align: right">区域: </el-col>
          <el-col :span="11">{{ setObj.area }}</el-col>
          <el-col :span="11" style="text-align: right">编号: </el-col>
          <el-col :span="11">{{ setObj.code }}</el-col>
          <el-col :span="11" style="text-align: right">货柜高度: </el-col>
          <el-col :span="11">{{ setObj.height }}mm</el-col>
          <el-col :span="11" style="text-align: right">托盘宽度: </el-col>
          <el-col :span="11">{{ setObj.trayWidth }}mm</el-col>
          <el-col :span="11" style="text-align: right">托盘长度: </el-col>
          <el-col :span="11">{{ setObj.trayLength }}mm</el-col>
        </el-row>

        <div class="btn">
          <el-button type="primary" @click="setBtn1">设 置</el-button>
        </div>
      </div>
    </div>

    <HuoGuiModal ref="HUoGuiRef" @on-success="getHuoGuiDataApiFn"></HuoGuiModal>
  </div>
</template>

<script setup>
import { getHuoGuiDataApi } from "@/api/home/<USER>";
const HuoGuiModal = defineAsyncComponent(() => import("./Modal/HuoGuiModal.vue"));

const HUoGuiRef = ref(null);
const setBtn1 = () => {
  HUoGuiRef.value.openModal(setObj.value);
};

const setObj = ref({});
const getHuoGuiDataApiFn = async () => {
  setObj.value = await getHuoGuiDataApi(localStorage.getItem("huogui.id"));
};

getHuoGuiDataApiFn();
</script>

<style lang="scss" scoped>
.com-border {
  width: 400px;
  height: 230px;
  border: 1px solid #c1c1c1;

  .title {
    height: 40px;
    line-height: 40px;
    background-color: #fafafa;
    border-bottom: 1px solid #c1c1c1;
    padding-left: 20px;
    font-weight: bold;
  }

  .text {
    padding-left: 20px;

    .el-col {
      margin-top: 15px;
      margin-right: 10px;
    }
    .btn {
      margin-top: 30px;
      text-align: center;
    }
  }
}
</style>
