<template>
  <div>
    <el-dialog title="设置" v-model="dialogueFlag" width="1020px" append-to-body>
      <el-row style="padding: 20px">
        <el-col :span="12">
          <img
            src="@/assets/images/tuopan.jpg"
            alt="托盘"
            style="width: 100%; height: 100%"
          />
        </el-col>

        <el-col :span="12">
          <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="120px">
            <el-form-item label="长度(mm):" prop="length">
              <div style="display: flex">
                <div style="margin-right: 10px">
                  <el-input-number
                    v-model="addForm.length"
                    controls-position="right"
                    :min="0"
                    style="width: 100%"
                  />
                </div>
                <span> mm</span>
              </div>
            </el-form-item>
            <el-form-item label="宽度(mm):" prop="width">
              <div style="display: flex">
                <div style="margin-right: 10px">
                  <el-input-number
                    v-model="addForm.width"
                    controls-position="right"
                    :min="0"
                    style="width: 100%"
                  />
                </div>
                <span> mm</span>
              </div>
            </el-form-item>
            <el-form-item label="高度(mm):" prop="height">
              <div style="display: flex">
                <div style="margin-right: 10px">
                  <el-input-number
                    v-model="addForm.height"
                    controls-position="right"
                    :min="0"
                    style="width: 100%"
                  />
                </div>
                <span>mm</span>
              </div>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { updateTrayTypeDetailApi } from "@/api/home/<USER>";
const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  length: [{ required: true, message: "请输入", trigger: "blur" }],
  width: [{ required: true, message: "请输入", trigger: "blur" }],
  height: [{ required: true, message: "请输入", trigger: "blur" }],
});

// 初始化弹窗
function openModal(val) {
  addForm.value = JSON.parse(JSON.stringify(val));
  dialogueFlag.value = true;
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      requestObj = updateTrayTypeDetailApi(addForm.value);
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("设置成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
