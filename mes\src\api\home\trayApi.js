/*
 * @Author: 方志良 
 * @Date: 2025-07-02 14:02:12
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 14:14:50
 * @FilePath: \aosikai-cabinet-wms-web\base\src\api\home\trayApi.js
 * 
 */
import request from '@/utils/request';

//托盘类型详情
export const getTrayTypeDetailApi = (id) => {
  return request({
    url: `/factory/tray/type/${id}`,
    method: 'get',
  });
}

//托盘类型编辑
export const updateTrayTypeDetailApi = (data) => {
  return request({
    url: '/factory/tray/type/update',
    method: 'put',
    data
  });
}

//托盘分页
export const getTrayPageApi = (params) => {
  return request({
    url: `/factory/tray/table`,
    method: 'get',
    params
  });
}

//托盘类型新增
export const addTrayPageApi = (data) => {
  return request({
    url: '/auto/task/tray/init',
    method: 'post',
    data
  });
}

//托盘类型编辑
export const editTrayPageApi = (data) => {
  return request({
    url: '/factory/tray/update',
    method: 'put',
    data
  });
}

//托盘类型删除
export const deleteTrayPageApi = (id) => {
  return request({
    url: `/factory/tray/del/${id}`,
    method: 'delete',
  });
}