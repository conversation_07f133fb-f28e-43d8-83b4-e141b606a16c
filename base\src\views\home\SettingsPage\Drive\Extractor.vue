<!--
 * @Author: 方志良 
 * @Date: 2025-07-03 09:26:05
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-03 09:58:20
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Drive\Extractor.vue
 * 
-->
<template>
  <div>
    <div class="com-border" style="height: 400px">
      <div class="title">运行设置</div>
      <div class="text">
        <el-row>
          <el-col :span="11" style="text-align: right">自动速度: </el-col>
          <el-col :span="11">{{ setObj.speedValue }}m/s</el-col>
          <el-col :span="11" style="text-align: right">手动速度: </el-col>
          <el-col :span="11">{{ setObj.manualSpeed }}m/s</el-col>
          <el-col :span="11" style="text-align: right">减速度: </el-col>
          <el-col :span="11">{{ setObj.deceleration }}m/s</el-col>
          <el-col :span="11" style="text-align: right">定位窗口: </el-col>
          <el-col :span="11">{{ setObj.position }}</el-col>
          <el-col :span="11" style="text-align: right">取托盘位置: </el-col>
          <el-col :span="11">{{ setObj.getPosition }}</el-col>
          <el-col :span="11" style="text-align: right">卸托盘位置: </el-col>
          <el-col :span="11">{{ setObj.putPosition }}</el-col>
        </el-row>

        <div class="btn">
          <el-button type="primary" @click="setBtn1">设 置</el-button>
        </div>
      </div>
    </div>

    <DriveSet ref="DriveSetRef" @on-success="getExtractorDataApiFn"></DriveSet>
  </div>
</template>

<script setup>
import { getExtractorDataApi } from "@/api/home/<USER>";
const DriveSet = defineAsyncComponent(() => import("../Modal/ExtractorSet.vue"));

const DriveSetRef = ref(null);
const setBtn1 = () => {
  DriveSetRef.value.openModal(setObj.value);
};

const setObj = ref({});
const getExtractorDataApiFn = async () => {
  setObj.value = await getExtractorDataApi(localStorage.getItem("huogui.id"));
};

getExtractorDataApiFn();
</script>

<style lang="scss" scoped></style>
