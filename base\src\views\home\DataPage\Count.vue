<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 09:41:22
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 10:12:22
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\DataPage\Count.vue
 * 
-->
<template>
  <div>
    <div class="top">
      <div class="com-border">
        <div class="title">运行里程</div>
        <div class="text">
          <el-row>
            <el-col :span="8"></el-col>
            <el-col :span="8">总计</el-col>
            <el-col :span="8">维护后</el-col>

            <el-col :span="8">升降机:</el-col>
            <el-col :span="8">100m</el-col>
            <el-col :span="8">100m</el-col>

            <el-col :span="8">提取器:</el-col>
            <el-col :span="8">85次</el-col>
            <el-col :span="8">85次</el-col>

            <el-col :span="8">指示灯x轴:</el-col>
            <el-col :span="8">85次</el-col>
            <el-col :span="8">85次</el-col>

            <el-col :span="8">安全门:</el-col>
            <el-col :span="8">85次</el-col>
            <el-col :span="8">85次</el-col>

            <el-col :span="8">升降倾斜:</el-col>
            <el-col :span="8">85次</el-col>
            <el-col :span="8">85次</el-col>
          </el-row>
        </div>
      </div>
      <div class="com-border" style="margin-left: 10px">
        <div class="title">运行时长</div>
        <div class="text">
          <el-row>
            <el-col :span="8"></el-col>
            <el-col :span="8">总计</el-col>
            <el-col :span="8">维护后</el-col>

            <el-col :span="8">立柜:</el-col>
            <el-col :span="8">100m</el-col>
            <el-col :span="8">100m</el-col>

            <el-col :span="8">升降机:</el-col>
            <el-col :span="8">85次</el-col>
            <el-col :span="8">85次</el-col>

            <el-col :span="8">提取器:</el-col>
            <el-col :span="8">85次</el-col>
            <el-col :span="8">85次</el-col>

            <el-col :span="8">指示灯x轴:</el-col>
            <el-col :span="8">85次</el-col>
            <el-col :span="8">85次</el-col>

            <el-col :span="8">安全门:</el-col>
            <el-col :span="8">85次</el-col>
            <el-col :span="8">85次</el-col>

            <el-col :span="8">升降倾斜:</el-col>
            <el-col :span="8">85次</el-col>
            <el-col :span="8">85次</el-col>
          </el-row>
        </div>
      </div>
    </div>

    <div class="bottom">
      <div>下次维护日期： 2025-10-10</div>

      <div>距离上次维护天数： 30 天</div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.top {
  display: flex;

  .com-border {
    width: 400px;
    height: 320px;
    border: 1px solid #c1c1c1;

    .title {
      height: 40px;
      line-height: 40px;
      background-color: #fafafa;
      border-bottom: 1px solid #c1c1c1;
      padding-left: 20px;
      font-weight: bold;
    }

    .text {
      padding-left: 20px;

      .el-col {
        margin-top: 15px;
      }
    }
  }
}

.bottom {
  margin-top: 20px;
  display: flex;

  div {
    flex: 1;
  }
}
</style>
