<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 09:41:22
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 10:18:12
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\DataPage\Communcate.vue
 * 
-->
<template>
  <div>
    <div class="top">
      <div class="com-border">
        <div class="title">发送 PLC-上位</div>
        <div class="text">
          <el-row>
            <el-col :span="12">IP:</el-col>
            <el-col :span="12">127.0.0.1</el-col>

            <el-col :span="12">Port:</el-col>
            <el-col :span="12">2010</el-col>

            <el-col :span="12">接收:</el-col>
            <el-col :span="12">1528</el-col>

            <el-col :span="12">发送:</el-col>
            <el-col :span="12">1528</el-col>

            <el-col :span="12"></el-col>
            <el-col :span="12"></el-col>

            <el-col :span="12">总计:</el-col>
            <el-col :span="12">1528</el-col>

            <el-col :span="12">TT7010:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>
          </el-row>
        </div>
      </div>
      <div class="com-border" style="margin-left: 10px">
        <div class="title">运行时长</div>
        <div class="text">
          <el-row>
            <el-col :span="12">IP:</el-col>
            <el-col :span="12">127.0.0.1</el-col>

            <el-col :span="12">Port:</el-col>
            <el-col :span="12">2010</el-col>

            <el-col :span="12">接收:</el-col>
            <el-col :span="12">1528</el-col>

            <el-col :span="12">发送:</el-col>
            <el-col :span="12">1528</el-col>

            <el-col :span="12"></el-col>
            <el-col :span="12"></el-col>

            <el-col :span="12">总计:</el-col>
            <el-col :span="12">1528</el-col>

            <el-col :span="12">TT7010:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>

            <el-col :span="12">TT7011:</el-col>
            <el-col :span="12">22</el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.top {
  display: flex;

  .com-border {
    width: 300px;
    height: 550px;
    border: 1px solid #c1c1c1;

    .title {
      height: 40px;
      line-height: 40px;
      background-color: #fafafa;
      border-bottom: 1px solid #c1c1c1;
      padding-left: 20px;
      font-weight: bold;
    }

    .text {
      padding-left: 20px;

      .el-col {
        margin-top: 15px;
      }
    }
  }
}
</style>
