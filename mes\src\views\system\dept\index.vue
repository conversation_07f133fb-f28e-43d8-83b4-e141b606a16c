<!--
 * @Author: 方志良 
 * @Date: 2024-03-29 17:18:13
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-08-13 14:44:17
 * @FilePath: \biweiman-mes-wms-front-end\src\views\system\dept\index.vue
 * 
-->
<template>
  <div class="app-container">

    <div class="top-search">
      <div>
        <el-button type="primary" @click="handleAdd" v-hasPermission="['system:dept:add']">添加部门</el-button>
        <el-button @click="toggleExpandAll">展开/折叠</el-button>
      </div>

      <SearchList v-model="queryParams.keywords" @update:modelValue="handleQuery"></SearchList>
    </div>

    <el-table v-if="refreshTable" v-loading="loading" :data="deptList" row-key="id" :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column prop="deptName" align="center" label="部门名称" width="260"></el-table-column>
      <el-table-column prop="orderNum" align="center" label="排序" width="200"></el-table-column>
      <!-- <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <span :style="{ color: scope.row.status == 1 ? '#008ca1' : '#fc6302' }">● </span>
          <span>{{ proxy.getDictLbelFn('sys_status', scope.row.status) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column prop="leaderName" align="center" label="负责人" width="200"></el-table-column>
      <el-table-column prop="phone" align="center" label="部门电话" width="200"></el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" align="center" label="备注" width="200"></el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button class="table-btn" text @click="handleUpdate(scope.row)"
            v-hasPermission="['system:dept:edit']">修改</el-button>
          <el-button class="table-btn" text @click="handleAdd(scope.row)"
            v-hasPermission="['system:dept:add']">新增</el-button>
          <el-button v-if="scope.row.parentId != 0" class="table-btn" text @click="handleDelete(scope.row)"
            v-hasPermission="['system:dept:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改部门对话框 -->
    <el-dialog :title="title" v-model="open" width="520px" append-to-body>
      <el-form ref="deptRef" :model="form" style="padding: 30px;" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24" v-if="form.parentId !== 0">
            <el-form-item label="上级部门" prop="parentId">
              <el-tree-select v-model="form.parentId" style="width:100%" :data="deptOptions"
                :props="{ value: 'id', label: 'deptName', children: 'children' }" node-key="id" placeholder="选择上级部门"
                check-strictly :current-node-key="form.parentId" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="部门名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="负责人">
              <el-select v-model="form.leaderId" filterable placeholder="请选择" style="width: 100%">
                <el-option v-for="item in userList" :key="item.userId" :label="`${item.nickname}-${item.username}`"
                  :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="部门电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input type="textarea"  maxlength="200"   :rows="2" placeholder="请输入" v-model="form.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Dept">
import * as deptApi from '@/api/system/deptApi';
import { listUserApi } from '@/api/system/user'
const { proxy } = getCurrentInstance();
const { sys_status } = proxy.useDict('sys_status');

const deptList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref('');
const deptOptions = ref([]);
const isExpandAll = ref(true);
const refreshTable = ref(true);

const data = reactive({
  form: {},
  queryParams: {
    // deptName: undefined,
    // status: undefined,
    keywords: ""
  },
  rules: {
    parentId: [{ required: true, message: '上级部门不能为空', trigger: 'blur' }],
    deptName: [{ required: true, message: '部门名称不能为空', trigger: 'blur' }],
    orderNum: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
    phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  },
});

const { queryParams, form, rules } = toRefs(data);


//成员
let userList = ref([])
const listUserApiFn = async () => {
  userList.value = await listUserApi()
}

/** 查询部门列表 */
function getList() {
  loading.value = true;
  deptApi
    .listDept(queryParams.value)
    .then((response) => {
      deptList.value = proxy.handleTree(response, 'id');
    })
    .finally(() => {
      loading.value = false;
    });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    deptId: undefined,
    parentId: undefined,
    deptName: undefined,
    orderNum: 0,
    leaderName: undefined,
    phone: undefined,
    email: undefined,

  };
  proxy.resetForm('deptRef');
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  deptApi.listDept().then((response) => {
    deptOptions.value = proxy.handleTree(response, 'id');
  });
  if (row != undefined) {
    form.value.parentId = row.deptId;
  }
  open.value = true;
  title.value = '添加部门';
}
/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  deptApi.listDept().then((response) => {
    deptOptions.value = proxy.handleTree(response, 'id');
  });
  form.value = JSON.parse(JSON.stringify(row));
  form.value.deptId = form.value.id
  form.value.leaderId = form.value.leaderId
  open.value = true;
  title.value = '修改部门';

}
/** 提交按钮 */
function submitForm() {
  proxy.$refs.deptRef.validate((valid) => {
    if (valid) {
      let userObj = userList.value.find(item => item.userId == form.value.leaderId)
      let leaderName = userObj ? userObj.nickname : ""
      if (form.value.deptId != undefined) {
        console.log(userList.value, form.value)
        deptApi.updateDept({ ...form.value, leaderName }).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        deptApi.addDeptApi({ ...form.value, leaderName }).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  console.log(row, 'main')
  proxy.$modal
    .confirm(`是否确认删除名称为"${row.deptName}"的数据项?`)
    .then(() => deptApi.deleteDept({ ids: [row.id] }))
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => { });
}

getList();

onMounted(() => {
  listUserApiFn()
})
</script>

<style lang="scss" scoped>
.top-search {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.table-btn {
  color: #008ca1 !important;
  margin: 5px;
  padding: 5px;
}
</style>
