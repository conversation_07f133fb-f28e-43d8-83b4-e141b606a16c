<!--
 * @Author: 方志良 
 * @Date: 2025-06-30 10:22:11
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-30 14:15:33
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\ManualPage\Extractor.vue
 * 
-->
<template>
  <div class="lift">
    <div class="left">
      <img src="@/assets/images/extractor.jpg" alt="升降机" />
      <div class="giuge">偏移距离: {{ currentOffset }}mm</div>
      <div class="giuge">当前速度: {{ currentSpeed }}m/s</div>
      <!-- <div class="giuge">当前载重: 1kg</div> -->
    </div>

    <!-- <div class="center">
      <div class="line" :style="{ bottom: linePosition + 'px' }">
        <div class="line-son"></div>
      </div>
    </div> -->

    <div class="text">
      <div>正转</div>
      <div>反转</div>
    </div>

    <div class="right">
      <div>
        <img
          v-if="upFlag == false"
          src="@/assets/images/top-off.png"
          alt="向上箭头-开"
          @click="upMoveFn"
        />
        <img
          v-else
          src="@/assets/images/top-on.png"
          alt="向上箭头-关"
          @click="upMoveFn"
        />
      </div>

      <div>
        <img
          src="@/assets/images/bottom-off.png"
          alt="向下箭头-开"
          v-if="downFlag == false"
          @click="downMoveFn"
        />
        <img
          src="@/assets/images/bottom-on.png"
          v-else
          alt="向上箭头-关"
          @click="downMoveFn"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { cabinetActionApi } from "@/api/home/<USER>";
import { ref, onUnmounted, onMounted } from "vue";

import { useStore } from "vuex";
import useWebSocket from "@/utils/useWebSocket";
const store = useStore();

const { proxy } = getCurrentInstance();
const manualFlag = inject("manualFlag");
// 初始位置和状态
const linePosition = ref(0); // 从底部开始的位置（px）
const currentOffset = ref(0); // 便宜距离（mm）
const currentSpeed = ref(0.0); // 当前速度（m/s）
const movingInterval = ref(null);
const totalHeight = 500; // 中心区域总高度（px）
const moveSpeed = 50; // 每秒移动50px（10秒到顶部）

//向上移动
let upFlag = ref(false); //默认关
const upMoveFn = () => {
  if (!manualFlag.value) return proxy.$modal.msgError("请先开启手动模式");
  upFlag.value = upFlag.value == true ? false : true;
  if (upFlag.value) {
    startMovingUp();
  } else {
    stopMoving();
  }
};

//向下移动
let downFlag = ref(false); //默认关
const downMoveFn = () => {
  if (!manualFlag.value) return proxy.$modal.msgError("请先开启手动模式");
  downFlag.value = downFlag.value == true ? false : true;
  if (downFlag.value) {
    startMovingDown();
  } else {
    stopMoving();
  }
};

// 开始反面移动
const startMovingUp = async () => {
  downFlag.value = false;


  await cabinetActionApi({
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: localStorage.getItem("outin.code"),
    deviceCode: 201,
    actionCode: 4,
  });

    connectWebsocketFn(4);
};

// 开始正面移动
const startMovingDown = async () => {
  upFlag.value = false;

  await cabinetActionApi({
    cabinetId: localStorage.getItem("huogui.id"),
    gateCode: localStorage.getItem("outin.code"),
    deviceCode: 201,
    actionCode: 3,
  });
  connectWebsocketFn(3);
};

// 停止移动
const stopMoving = () => {
  clearInterval(movingInterval.value);
  movingInterval.value = null;
  currentSpeed.value = 0.0;
};

//获取基本信息
const connectWebsocketFn = (code) => {
  const { connect } = useWebSocket();

  const buildParams = (actionCode) => {
    return () => ({
      userId: store.state.user.userinfo.userId,
      cabinetId: localStorage.getItem("huogui.id"),
      gateCode: localStorage.getItem("outin.code"),
      type: 2,
      manualActionDataCommand: {
        deviceCode: 201,
        actionCode,
      },
    });
  };

  const handleMessage = (res) => {
    currentOffset.value = res.data.offset;
    currentSpeed.value = res.data.currentSpeed;
  };

  connect(import.meta.env.VITE_APP_WEBSOCKET, buildParams(code), handleMessage);
};

onMounted(() => {
  connectWebsocketFn(3);
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (movingInterval.value) clearInterval(movingInterval.value);
});
</script>

<style lang="scss" scoped>
.lift {
  margin-top: 100px;
  display: flex;

  .left {
    img {
      width: 500px;
      height: 300px;
    }
    .giuge {
      color: #272727;
      margin-top: 20px;
      margin-left: 150px;
    }
  }

  .center {
    position: relative;
    height: 500px;
    margin-left: 50px;
    width: 10px;
    background-color: #e6e6e6;

    .line {
      position: absolute;
      left: 10px;
      bottom: 0;
      width: 10px;
      height: 60px;
      background-color: #162437;

      .line-son {
        position: absolute;
        left: 10px;
        bottom: 15px;
        width: 10px;
        height: 30px;
        background-color: #162437;
      }
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    margin-left: 50px;
    height: 500px;

    img {
      width: 75px;
      height: 55px;
      cursor: pointer;
    }
  }
}

.text {
  margin-left: 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
