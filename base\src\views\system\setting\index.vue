<!--
 * @Author: 方志良 
 * @Date: 2024-07-02 16:26:20
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-01-16 09:02:22
 * @FilePath: \biweiman-mes-wms-front-end\src\views\system\setting\index.vue
 * 
-->
<template>
  <div class="oc">
    <el-card class="card">

      <el-table :data="tableList">
        <el-table-column label="审核项目" prop="name" align="center"></el-table-column>
        <el-table-column label="一级审核" prop="firstLevel" align="center">
          <template #default="scope">
            <span>{{ scope.row.firstLevel ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="二级审核" prop="secondLevel" align="center">
          <template #default="scope">
            <span>{{ scope.row.secondLevel ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" align="center"></el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <div class="div-flex">
              <el-button class="table-btn" text @click="editTableBtn(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <SettingEdit v-model:isvisable="dialogFlag" :editObj="editObj" @onSuccess="thePageFn"></SettingEdit>
  </div>
</template>

<script setup>
const SettingEdit = defineAsyncComponent(() => import('./SettingEdit.vue'))
import { getSettingListApi } from '@/api/system/setApi'
const { proxy } = getCurrentInstance();

//分页
const tableList = ref([])
const thePageFn = async () => {
  tableList.value = await getSettingListApi()
}


//编辑
const editObj = ref({})
const editTableBtn = (row) => {
  editObj.value = row
  dialogFlag.value = true
}


//弹框
let dialogFlag = ref(false)



onMounted(() => {
  thePageFn()
})

</script>

<style lang="scss" scoped></style>