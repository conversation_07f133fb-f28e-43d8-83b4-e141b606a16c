@charset "UTF-8";
/**
$base-menu-color:hsla(0,0%,100%,.65);
$base-menu-color-active:#fff;
$base-menu-background:#001529;
$base-logo-title-color: #ffffff;

$base-menu-light-color:rgba(0,0,0,.70);
$base-menu-light-background:#ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background:#000c17;
$base-sub-menu-hover:#001528;
*/
:export {
  menuColor: #bccad1;
  menuLightColor: rgba(0, 0, 0, 0.623);
  menuColorActive: #f4f4f5;
  menuBackground: #003a5d;
  menuLightBackground: #003a5d;
  subMenuBackground: #305b6f;
  subMenuHover: #305b6f;
  sideBarWidth: 256px;
  logoTitleColor: #ffffff;
  logoLightTitleColor: #001529;
  primaryColor: #409eff;
  successColor: #67c23a;
  dangerColor: #f56c6c;
  infoColor: #909399;
  warningColor: #e6a23c;
}

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  position: absolute;
}

/**
$base-menu-color:hsla(0,0%,100%,.65);
$base-menu-color-active:#fff;
$base-menu-background:#001529;
$base-logo-title-color: #ffffff;

$base-menu-light-color:rgba(0,0,0,.70);
$base-menu-light-background:#ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background:#000c17;
$base-sub-menu-hover:#001528;
*/
:export {
  menuColor: #bccad1;
  menuLightColor: rgba(0, 0, 0, 0.623);
  menuColorActive: #f4f4f5;
  menuBackground: #003a5d;
  menuLightBackground: #003a5d;
  subMenuBackground: #305b6f;
  subMenuHover: #305b6f;
  sideBarWidth: 256px;
  logoTitleColor: #ffffff;
  logoLightTitleColor: #001529;
  primaryColor: #409eff;
  successColor: #67c23a;
  dangerColor: #f56c6c;
  infoColor: #909399;
  warningColor: #e6a23c;
}

#app .main-container {
  min-height: 100%;
  transition: margin-left .28s;
  margin-left: 256px;
  position: relative;
}

#app .sidebarHide {
  margin-left: 0 !important;
}

#app .sidebar-container {
  -webkit-transition: width .28s;
  transition: width 0.28s;
  width: 256px !important;
  background-color: #003a5d;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  -webkit-box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
}

#app .sidebar-container .horizontal-collapse-transition {
  transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}

#app .sidebar-container .scrollbar-wrapper {
  overflow-x: hidden !important;
}

#app .sidebar-container .el-scrollbar__bar.is-vertical {
  right: 0px;
}

#app .sidebar-container .el-scrollbar {
  height: 100%;
}

#app .sidebar-container.has-logo .el-scrollbar {
  height: calc(100% - 50px);
}

#app .sidebar-container .is-horizontal {
  display: none;
}

#app .sidebar-container a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}

#app .sidebar-container .svg-icon {
  margin-right: 16px;
}

#app .sidebar-container .el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}

#app .sidebar-container el-menu-item-active {
  background-color: red;
}

#app .sidebar-container .el-menu-item, #app .sidebar-container .menu-title {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

#app .sidebar-container .el-menu-item .el-menu-tooltip__trigger {
  display: inline-block !important;
}

#app .sidebar-container .sub-menu-title-noDropdown:hover,
#app .sidebar-container .el-sub-menu__title:hover {
  background-color: rgba(0, 0, 0, 0.06) !important;
}

#app .sidebar-container .theme-dark .is-active > .el-sub-menu__title {
  color: #f4f4f5 !important;
}

#app .sidebar-container .nest-menu .el-sub-menu > .el-sub-menu__title,
#app .sidebar-container .el-sub-menu .el-menu-item {
  min-width: 256px !important;
}

#app .sidebar-container .nest-menu .el-sub-menu > .el-sub-menu__title:hover,
#app .sidebar-container .el-sub-menu .el-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.06) !important;
}

#app .sidebar-container .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
#app .sidebar-container .theme-dark .el-sub-menu .el-menu-item {
  background-color: #305b6f !important;
}

#app .sidebar-container .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title:hover,
#app .sidebar-container .theme-dark .el-sub-menu .el-menu-item:hover {
  background-color: #305b6f !important;
  color: #ffffff;
}

#app .hideSidebar .sidebar-container {
  width: 54px !important;
}

#app .hideSidebar .main-container {
  margin-left: 54px;
}

#app .hideSidebar .sub-menu-title-noDropdown {
  padding: 0 !important;
  position: relative;
}

#app .hideSidebar .sub-menu-title-noDropdown .el-tooltip {
  padding: 0 !important;
}

#app .hideSidebar .sub-menu-title-noDropdown .el-tooltip .svg-icon {
  margin-left: 20px;
}

#app .hideSidebar .el-sub-menu {
  overflow: hidden;
}

#app .hideSidebar .el-sub-menu > .el-sub-menu__title {
  padding: 0 !important;
}

#app .hideSidebar .el-sub-menu > .el-sub-menu__title .svg-icon {
  margin-left: 20px;
}

#app .hideSidebar .el-menu--collapse .el-sub-menu > .el-sub-menu__title > span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

#app .hideSidebar .el-menu--collapse .el-sub-menu > .el-sub-menu__title > i {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

#app .el-menu--collapse .el-menu .el-sub-menu {
  min-width: 256px !important;
}

#app .mobile .main-container {
  margin-left: 0px;
}

#app .mobile .sidebar-container {
  transition: transform .28s;
  width: 256px !important;
}

#app .mobile.hideSidebar .sidebar-container {
  pointer-events: none;
  transition-duration: 0.3s;
  transform: translate3d(-256px, 0, 0);
}

#app .withoutAnimation .main-container,
#app .withoutAnimation .sidebar-container {
  transition: none;
}

.el-menu--vertical > .el-menu .svg-icon {
  margin-right: 16px;
}

.el-menu--vertical .nest-menu .el-sub-menu > .el-sub-menu__title:hover,
.el-menu--vertical .el-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.06) !important;
}

.el-menu--vertical > .el-menu--popup {
  max-height: 100vh;
  overflow-y: auto;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar {
  width: 6px;
}

.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}

/**
$base-menu-color:hsla(0,0%,100%,.65);
$base-menu-color-active:#fff;
$base-menu-background:#001529;
$base-logo-title-color: #ffffff;

$base-menu-light-color:rgba(0,0,0,.70);
$base-menu-light-background:#ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background:#000c17;
$base-sub-menu-hover:#001528;
*/
:export {
  menuColor: #bccad1;
  menuLightColor: rgba(0, 0, 0, 0.623);
  menuColorActive: #f4f4f5;
  menuBackground: #003a5d;
  menuLightBackground: #003a5d;
  subMenuBackground: #305b6f;
  subMenuHover: #305b6f;
  sideBarWidth: 256px;
  logoTitleColor: #ffffff;
  logoLightTitleColor: #001529;
  primaryColor: #409eff;
  successColor: #67c23a;
  dangerColor: #f56c6c;
  infoColor: #909399;
  warningColor: #e6a23c;
}

.el-button--primary {
  background-color: #006699 !important;
  border: none !important;
}

.blue-btn {
  background: #409eff;
}

.blue-btn:hover {
  color: #409eff;
}

.blue-btn:hover:before, .blue-btn:hover:after {
  background: #409eff;
}

.light-blue-btn {
  background: #3a71a8;
}

.light-blue-btn:hover {
  color: #3a71a8;
}

.light-blue-btn:hover:before, .light-blue-btn:hover:after {
  background: #3a71a8;
}

.red-btn {
  background: #ff4444;
}

.red-btn:hover {
  color: #ff4444;
}

.red-btn:hover:before, .red-btn:hover:after {
  background: #ff4444;
}

.pink-btn {
  background: #e65d6e;
}

.pink-btn:hover {
  color: #e65d6e;
}

.pink-btn:hover:before, .pink-btn:hover:after {
  background: #e65d6e;
}

.green-btn {
  background: #30b08f;
}

.green-btn:hover {
  color: #30b08f;
}

.green-btn:hover:before, .green-btn:hover:after {
  background: #30b08f;
}

.tiffany-btn {
  background: #33b5e5;
}

.tiffany-btn:hover {
  color: #33b5e5;
}

.tiffany-btn:hover:before, .tiffany-btn:hover:after {
  background: #33b5e5;
}

.yellow-btn {
  background: #ffbb33;
}

.yellow-btn:hover {
  color: #ffbb33;
}

.yellow-btn:hover:before, .yellow-btn:hover:after {
  background: #ffbb33;
}

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;
}

.pan-btn:hover {
  background: #fff;
}

.pan-btn:hover:before, .pan-btn:hover:after {
  width: 100%;
  transition: 600ms ease all;
}

.pan-btn:before, .pan-btn:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 2px;
  width: 0;
  transition: 400ms ease all;
}

.pan-btn::after {
  right: inherit;
  top: inherit;
  left: 0;
  bottom: 0;
}

.custom-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  color: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}

/**
 * 通用css样式布局处理
 * Copyright (c) 2019 ruoyi
 */
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload input[type='file'] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.cell .el-tag {
  margin-right: 0px;
}

.small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width .el-button--mini {
  padding: 7px 10px;
  width: 60px;
}

.status-col .cell {
  padding: 0 10px;
  text-align: center;
}

.status-col .cell .el-tag {
  margin-right: 0px;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container .el-upload {
  width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu a {
  display: block;
}

.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

/** 基础通用 **/
.pt5 {
  padding-top: 5px;
}

.pr5 {
  padding-right: 5px;
}

.pb5 {
  padding-bottom: 5px;
}

.mt5 {
  margin-top: 5px;
}

.mr5 {
  margin-right: 5px;
}

.mb5 {
  margin-bottom: 5px;
}

.mb8 {
  margin-bottom: 8px;
}

.ml5 {
  margin-left: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mt20 {
  margin-top: 20px;
}

.mr20 {
  margin-right: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

.ml20 {
  margin-left: 20px;
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}

.el-dialog:not(.is-fullscreen) {
  margin-top: 6vh !important;
}

.el-dialog.scrollbar .el-dialog__body {
  overflow: auto;
  overflow-x: hidden;
  max-height: 70vh;
  padding: 10px 20px 0;
}

.el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  word-break: break-word;
  background-color: #f8f8f9 !important;
  color: #515a6e;
  height: 40px !important;
  font-size: 13px;
}

.el-table .el-table__body-wrapper .el-button [class*='el-icon-'] + span {
  margin-left: 1px;
}

/** 表单布局 **/
.form-header {
  font-size: 15px;
  color: #6379bb;
  border-bottom: 1px solid #ddd;
  margin: 8px 10px 25px 10px;
  padding-bottom: 5px;
}

/** 表格布局 **/
.pagination-container {
  height: 25px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding: 10px 20px !important;
}

/* tree border */
.tree-border {
  margin-top: 5px;
  border: 1px solid #e5e6e7;
  background: #ffffff none;
  border-radius: 4px;
  width: 100%;
}

.pagination-container .el-pagination {
  right: 0;
  position: absolute;
}

@media (max-width: 768px) {
  .pagination-container .el-pagination > .el-pagination__jump {
    display: none !important;
  }
  .pagination-container .el-pagination > .el-pagination__sizes {
    display: none !important;
  }
}

.el-table .fixed-width .el-button--small {
  padding-left: 0;
  padding-right: 0;
  width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
  cursor: pointer;
  color: #409eff;
  margin-left: 10px;
}

.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 12px;
}

.el-tree-node__content > .el-checkbox {
  margin-right: 8px;
}

.list-group-striped > .list-group-item {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
}

.list-group {
  padding-left: 0px;
  list-style: none;
}

.list-group-item {
  border-bottom: 1px solid #e7eaec;
  border-top: 1px solid #e7eaec;
  margin-bottom: -1px;
  padding: 11px 0px;
  font-size: 13px;
}

.pull-right {
  float: right !important;
}

.el-card__header {
  padding: 14px 15px 7px !important;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px !important;
}

.card-box {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  background: #20b2aa;
  border-color: #20b2aa;
  color: #ffffff;
}

.el-button--cyan:focus,
.el-button--cyan:hover {
  background: #48d1cc;
  border-color: #48d1cc;
  color: #ffffff;
}

.el-button--cyan {
  background-color: #20b2aa;
  border-color: #20b2aa;
  color: #ffffff;
}

/* text color */
.text-navy {
  color: #1ab394;
}

.text-primary {
  color: inherit;
}

.text-success {
  color: #1c84c6;
}

.text-info {
  color: #23c6c8;
}

.text-warning {
  color: #f8ac59;
}

.text-danger {
  color: #ed5565;
}

.text-muted {
  color: #888888;
}

/* image */
.img-circle {
  border-radius: 50%;
}

.img-lg {
  width: 120px;
  height: 120px;
}

.avatar-upload-preview {
  position: absolute;
  top: 50%;
  transform: translate(50%, -50%);
  width: 200px;
  height: 200px;
  border-radius: 50%;
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
}

/* 拖拽列样式 */
.sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #42b983 !important;
}

/* 表格右侧工具栏样式 */
.top-right-btn {
  margin-left: auto;
}

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: ' ';
  clear: both;
  height: 0;
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans',
 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

aside a {
  color: #337ab7;
  cursor: pointer;
}

aside a:hover {
  color: #20a0ff;
}

.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, #20b6f9 0%, #20b6f9 0%, #2178f1 100%, #2178f1 100%);
}

.sub-navbar .subtitle {
  font-size: 20px;
  color: #fff;
}

.sub-navbar.draft {
  background: #d0d0d0;
}

.sub-navbar.deleted {
  background: #d0d0d0;
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
}

.link-type:hover,
.link-type:focus:hover {
  color: #20a0ff;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}

.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}
