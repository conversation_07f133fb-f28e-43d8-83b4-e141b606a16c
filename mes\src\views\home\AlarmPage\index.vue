<!--
 * @Author: 方志良 
 * @Date: 2025-06-26 17:07:20
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 13:47:42
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\AlarmPage\index.vue
 * 
-->
<template>
  <div class="app-container oc" style="background-color: #fff">
    <el-tabs v-model="activeName" :stretch="true" type="card" @tab-click="handleClick">
      <el-tab-pane label="当前报警" :name="1"></el-tab-pane>
      <el-tab-pane label="历史记录" :name="2"></el-tab-pane>
    </el-tabs>

    <div class="action">
      <div></div>
      <div class="selection">
        <label v-if="activeName === 2">日期:</label>
        <el-date-picker
          v-if="activeName === 2"
          value-format="YYYY-MM-DD"
          style="width: 240px"
          @change="getListFn"
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <SearchList
          v-model="queryParams.keywords"
          @update:modelValue="getListFn"
        ></SearchList>
      </div>
    </div>

    <el-table v-loading="loading" :data="tableList">
      <el-table-column label="时间" align="center" prop="recordDate" />
      <el-table-column label="类型" align="center" prop="warningTypeLabel" />
      <el-table-column label="代码" align="center" prop="warningCode" />
      <el-table-column label="描述" align="center" prop="warningDescription" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <div class="div-flex">
            <el-button link text class="table-btn" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="dataTotol > 0"
      :total="dataTotol"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getListFn"
    />
  </div>
</template>

<script setup>
import { getNowAlarmPageApi, getHistoryAlarmPageApi } from "@/api/home/<USER>";
let activeName = ref(1);

//表格
const dataTotol = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  dateRange: [],
});
const loading = ref(false);
let tableList = ref([]);
const getListFn = () => {
  loading.value = true;
  let requestObj = null;
  if (activeName.value === 1) {
    requestObj = getNowAlarmPageApi({
      cabinetId: localStorage.getItem("huogui.id"),
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      keywords: queryParams.value.keywords,
    });
  } else {
    requestObj = getHistoryAlarmPageApi({
      cabinetId: localStorage.getItem("huogui.id"),
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      keywords: queryParams.value.keywords,
      queryStartDate: queryParams.value.dateRange ? queryParams.value.dateRange[0] : null,
      queryEndDate: queryParams.value.dateRange ? queryParams.value.dateRange[1] : null,
    });
  }
  requestObj
    .then((res) => {
      tableList.value = res.rows;
      dataTotol.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

//删除
const handleDelete = (row) => {
  // proxy.$modal
  //   .confirm(`是否确认删除？`)
  //   .then(() => deleteCaijiRecordPageApi(row.id))
  //   .then(() => {
  //     getListFn();
  //     proxy.$modal.msgSuccess("删除成功");
  //   })
  //   .catch(() => {});
};

//切换
const handleClick = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    dateRange: [],
  };
  nextTick(() => {
    getListFn();
  });
};

onMounted(() => {
  getListFn();
});
</script>

<style lang="scss" scoped>
.el-tabs {
  width: 20%;
  margin: 0 auto;
}
</style>
