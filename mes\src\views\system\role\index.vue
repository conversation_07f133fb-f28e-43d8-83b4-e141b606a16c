<!--
 * @Author: 方志良 
 * @Date: 2024-03-29 17:18:13
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-04-17 15:50:04
 * @FilePath: \biweiman-mes-wms-front-end\src\views\system\role\index.vue
 * 
-->
<template>
  <div class="tc">
    <RoleLeft @onSuccess="onSuccessFn"></RoleLeft>
    <RolrRight :menuId="menuId"></RolrRight>
  </div>
</template>

<script setup>
const RoleLeft = defineAsyncComponent(() => import('./roleLeft.vue'))
const RolrRight = defineAsyncComponent(() => import('./rolrRight.vue'))

const menuId = ref("")
const onSuccessFn = (val) => {
  menuId.value = val
}
</script>

<style lang="scss" scoped></style>