<!--
 * @Author: 方志良 
 * @Date: 2024-04-08 15:53:26
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-04-24 19:56:52
 * @FilePath: \biweiman-mes-wms-front-end\src\components\iFrame\index.vue
 * 
-->
<template>
  <div v-loading="loading" :style="'height:' + height">
    <iframe :src="url" frameborder="no" style="width: 100%; height: 100%" scrolling="auto" />
  </div>
</template>

<script setup>
const props = defineProps({
  src: {
    type: String,
    required: true,
  },
});

const height = ref(`${document.documentElement.clientHeight - 114}px;`);
const loading = ref(true);
const url = computed(() => props.src);

onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 300);
  window.onresize = function temp() {
    height.value = `${document.documentElement.clientHeight - 94.5}px;`;
  };
});
</script>
