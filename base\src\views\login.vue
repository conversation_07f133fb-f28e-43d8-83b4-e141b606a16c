<!--
 * @Author: babyyage
 * @Date: 2025-04-09 10:06:18
 * @LastEditTime: 2025-06-27 11:41:24
-->
<template>
  <div>
    <TopHeader TopTitle="登录" :showLogout="false"></TopHeader>
    <div class="login">
      <el-form ref="loginRef" label-width="80px" :model="loginForm" :rules="loginRules">
        <div style="display: flex;">

          <el-form-item prop="username" label="用户名">
            <el-input v-model="loginForm.username" ref="inputRef" auto-complete="off" placeholder="账号">

            </el-input>
          </el-form-item>

          <el-form-item prop="password" label="密码">
            <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
              @keyup.enter="handleLogin">

            </el-input>
          </el-form-item>
          <el-button :loading="loading" style="margin-left: 10px;" type="primary" @click.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <el-button @click="cancel">重 置</el-button>
          <el-button @click="returnBtn">返 回</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import Cookies from 'js-cookie';
import { encrypt, decrypt } from '@/utils/rsaUtil';

const store = useStore();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: '',
  password: '',
  rememberMe: true,
});

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
};


const loading = ref(false);


function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在cookie中设置记住用户明和名命
      if (loginForm.value.rememberMe) {
        Cookies.set('username', loginForm.value.username, { expires: 30 });
        Cookies.set('password', encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set('rememberMe', loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove('username');
        Cookies.remove('password');
        Cookies.remove('rememberMe');
      }
      // 调用action的登录方法
      store
        .dispatch('Login', loginForm.value)
        .then(() => {
          // router.push({ path: redirect.value || '/401' });
          router.push({ path: '/' });
        })
        .catch(() => {
          loading.value = false;
        }).finally(() => {
          loading.value = false;
        });
    }
  });
}
  
function getCookie() {
  const username = Cookies.get('username');
  const password = Cookies.get('password');
  const rememberMe = Cookies.get('rememberMe');
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

getCookie();


//重置
const cancel = () => {
  loginForm.value = {}
}
//返回
const returnBtn = () => {
  router.push({ path: '/ExitPage' });
}

</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  margin-top: 100px;
  background-color: #ffffff;
}
</style>
