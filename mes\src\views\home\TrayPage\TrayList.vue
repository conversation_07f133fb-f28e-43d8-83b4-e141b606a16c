<template>
  <div class="oc" style="background-color: #fff">
    <div class="action">
      <div>
        <el-button type="primary" @click="handleAddEdit({})">添加</el-button>
      </div>
      <div class="selection">
        <SearchList
          v-model="searchForms.keywords"
          @update:modelValue="theResetPageFn"
        ></SearchList>
      </div>
    </div>

    <el-table :data="tableList">
      <el-table-column label="托盘号" prop="trayCode" align="center"></el-table-column>
      <el-table-column label="托盘名称" prop="trayName" align="center"></el-table-column>
      <el-table-column label="最大负载kg" prop="maxLoad" align="center">
      </el-table-column>
      <el-table-column label="倾斜角度" prop="tiltAngle" align="center">
      </el-table-column>
      <el-table-column label="出入口" prop="gateCode" align="center"> </el-table-column>
      <el-table-column label="方向" prop="direction" align="center"> </el-table-column>
      <el-table-column label="当前高度mm" prop="mapHeight" align="center">
      </el-table-column>
      <el-table-column label="可用状态" prop="status" align="center">
        <template #default="scope">
          <!-- <span :style="{ color: scope.row.status === 0 ? '#fc6302' : '#008ca1' }"
            >●
          </span> -->
          <span>{{ scope.row.statusLabel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用状态" prop="status" align="center">
        <template #default="scope">
          <!-- <span :style="{ color: scope.row.status === 0 ? '#fc6302' : '#008ca1' }"
            >●
          </span> -->
          <span>{{ scope.row.trayUseStatusLabel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template #default="scope">
          <div class="div-flex">
            <el-button class="table-btn" text @click="handleAddEdit(scope.row)"
              >编辑</el-button
            >
            <el-button class="table-btn" text @click="deleteBtn(scope.row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row justify="end" style="margin-top: 10px">
      <el-pagination
        v-show="thePage.total > 0"
        background
        :total="thePage.total"
        v-model:current-page="thePage.current"
        v-model:page-size="thePage.size"
        @current-change="thePageFn"
        layout="total,prev, pager, next"
      >
      </el-pagination>
    </el-row>

    <ListMOdal ref="addEdieModalRef" @on-success="theResetPageFn"></ListMOdal>
  </div>
</template>

<script setup>
import { getTrayPageApi, deleteTrayPageApi } from "@/api/home/<USER>";
const ListMOdal = defineAsyncComponent(() => import("./modal/ListMOdal.vue"));
const { proxy } = getCurrentInstance();

//分页
const thePage = ref({
  current: 1,
  size: proxy.configs.pageSize,
  total: 0,
});
const tableList = ref([]);
const thePageFn = async () => {
  let obj = {
    cabinetId: localStorage.getItem("huogui.id"),
    pageNum: thePage.value.current,
    pageSize: thePage.value.size,
    keywords: searchForms.value.keywords,
  };
  let res = await getTrayPageApi(obj);
  tableList.value = res.rows;
  thePage.value.total = res.total;
};

//搜索
const searchForms = ref({
  keywords: "",
});
const theResetPageFn = () => {
  thePage.value.current = 1;
  thePageFn();
};

//编辑
const addEdieModalRef = ref(null);
const handleAddEdit = (row) => {
  addEdieModalRef.value.openModal(row);
};

//删除
const deleteBtn = (row) => {
  proxy.$modal
    .confirm(`是否确认删除此数据项？`)
    .then(() => deleteTrayPageApi(row.trayId))
    .then(() => {
      proxy.$modal.msgSuccess(`删除成功`);
      thePageFn();
    })
    .catch(() => {});
};

onMounted(() => {
  thePageFn();
});
</script>

<style lang="scss" scoped></style>
