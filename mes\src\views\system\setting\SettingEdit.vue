<template>
  <el-dialog v-model="isvisable" draggable title="编辑" align-center width="520px" @close="cancelBtn">
    <el-form ref="dialogRef" :model="dialogueForm" label-width="80px" style="padding:30px" :rules="rules">
      <el-form-item label="审核项目">
        <el-input v-model="dialogueForm.name" disabled placeholder="请输入" />
      </el-form-item>
      <el-form-item label="一级审核">
        <el-input v-model="dialogueForm.firstLevelName" disabled placeholder="请输入" />
      </el-form-item>

      <el-form-item label="二级审核" prop="secondLevel">
        <el-select v-model="dialogueForm.secondLevel" placeholder="请选择" style="width: 100%">
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea" maxlength="200" :rows="2" placeholder="请输入" v-model="dialogueForm.remark"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirmBtn">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { edtSettingListApi } from '@/api/system/setApi'
const { proxy } = getCurrentInstance();
const { sys_status } = proxy.useDict('sys_status');
let props = defineProps({
  isvisable: {
    type: Boolean,
    default: false,
  },
  editObj: {
    type: Object,
    default: () => { },
  }
});

const dialogueForm = ref({})
const rules = reactive(proxy.configs.formRules(['secondLevel']))

watchEffect(() => {
  if (props.isvisable && Object.keys(props.editObj).length > 0) {
    dialogueForm.value.name = props.editObj.name
    dialogueForm.value.secondLevel = props.editObj.secondLevel
    dialogueForm.value.firstLevelName = '是'
  }
});

const emit = defineEmits(['update:isvisable', 'onSuccess']);
//取消
const dialogRef = ref(null)
const cancelBtn = () => {
  dialogueForm.value = {}
  emit('update:isvisable', false);
  dialogRef.value.resetFields()
}

//确定
let loading = ref(false)
const confirmBtn = async () => {
  let flag = await dialogRef.value.validate()
  if (flag) {
    try {
      loading.value = true
      await edtSettingListApi({
        auditConfigId: props.editObj.auditConfigId,
        firstLevel: true,
        secondLevel: dialogueForm.value.secondLevel,
        remark: dialogueForm.value.remark,
      })
      proxy.$modal.msgSuccess(`编辑成功`);
      cancelBtn()
      emit('onSuccess');
    } finally {
      loading.value = false
    }
  }
}

onMounted(() => {
})
</script>

<style lang="scss" ></style>