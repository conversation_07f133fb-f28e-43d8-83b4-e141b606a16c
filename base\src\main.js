/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 16:32:12
 * @FilePath: \aosikai-cabinet-wms-web\base\src\main.js
 * 
 */
/*
 * @Author: 方志良
 * @Date: 2024-03-29 17:18:13
 * @LastEditors: 方志良
 * @LastEditTime: 2024-06-24 14:28:22
 * @FilePath: \biweiman-mes-wms-front-end\src\main.js
 *
 */
import { createApp } from 'vue';

import Cookies from 'js-cookie';

import ElementPlus from 'element-plus';
import locale from 'element-plus/lib/locale/lang/zh-cn'; // 中文语言

import '@/assets/styles/index.scss'; // global css
import print from 'vue3-print-nb';

import App from './App';
import store from './store';
import router from './router';
import directive from './directive'; // directive

// 注册指令
import plugins from './plugins'; // plugins
import { download, exportLoad } from '@/utils/request';

// svg图标
import 'virtual:svg-icons-register';
import SvgIcon from '@/components/SvgIcon';
import elementIcons from '@/components/SvgIcon/svgicon';

import './router/interceptor'; // permission control

import { resetForm, addTimeRange, handleTree } from '@/utils/common';
import { useDict, selectDictLabel, getDictLbelFn } from '@/utils/dict';
import { parseTime } from '@/utils/dateUtil';

// 分页组件
import Pagination from '@/components/Pagination';
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar';
// 文件上传组件
import FileUpload from '@/components/FileUpload';
// 图片上传组件
import ImageUpload from '@/components/ImageUpload';
// 图片预览组件
import ImagePreview from '@/components/ImagePreview';
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect';
// 字典标签组件
import DictTag from '@/components/DictTag';
// 数字金额
import AmountTag from '@/components/AmountTag';
// 搜索
import SearchList from '@/components/Search/index.vue';
import configs from '@/utils/config';
// 左右分割
import DragLayout from '@/components/DragLayout/index.vue';

//头顶
import TopHeader from '@/views/component/TopHeader.vue'

//loading
import { ElLoading } from 'element-plus'

const app = createApp(App);


// 开启全局loading
const openFullLoading = (text) => {
  const fullLoading = ElLoading.service({
    lock: false,
    text: text || '托盘初始化入库中，请稍等',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  return fullLoading
}
app.config.globalProperties.openFullLoading = openFullLoading

// 全局方法挂载
app.config.globalProperties.useDict = useDict;
app.config.globalProperties.getDictLbelFn = getDictLbelFn;
app.config.globalProperties.download = download;
app.config.globalProperties.exportLoad = exportLoad;
app.config.globalProperties.parseTime = parseTime;
app.config.globalProperties.resetForm = resetForm;
app.config.globalProperties.handleTree = handleTree;
app.config.globalProperties.addTimeRange = addTimeRange;
app.config.globalProperties.selectDictLabel = selectDictLabel;
app.config.globalProperties.configs = configs;

// 全局组件挂载
app.component('DictTag', DictTag);
app.component('AmountTag', AmountTag);
app.component('Pagination', Pagination);
app.component('TreeSelect', TreeSelect);
app.component('FileUpload', FileUpload);
app.component('ImageUpload', ImageUpload);
app.component('ImagePreview', ImagePreview);
app.component('RightToolbar', RightToolbar);
app.component('SearchList', SearchList);
app.component('DragLayout', DragLayout);
app.component('TopHeader', TopHeader);

app.use(router);
app.use(store);
app.use(plugins);
app.use(elementIcons);
app.use(print);
app.component('SvgIcon', SvgIcon);

directive(app);

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale,
  // 支持 large、default、small
  size: Cookies.get('size') || 'default'
});

app.mount('#app');
