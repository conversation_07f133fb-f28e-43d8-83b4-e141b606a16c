/*
 * @Author: 方志良
 * @Date: 2024-04-01 14:16:59
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-09 15:11:19
 * @FilePath: \mewyeah-mes-front-end\src\utils\config.js
 *
 */
/*
 * @Author: 方志良
 * @Date: 2024-04-01 14:16:59
 * @LastEditors: 方志良
 * @LastEditTime: 2024-04-08 13:08:50
 * @FilePath: \mewyeah-mes-front-end\src\utils\config.js
 *
 */
export default {
  //页面大小
  pageSize: 10,
  //图片显示
  getImageUrl: (img) => {
    return img ? `${import.meta.env.VITE_APP_BASE_API}${img}` : '';
  },
  //非空校验
  formRules: (arr) => {
    if (!Array.isArray(arr)) throw new Error('请传入一个数组');
    return arr.reduce((acc, key) => {
      acc[key] = [{ required: true, message: '此处不能为空' }];
      return acc;
    }, {});
  },
};
