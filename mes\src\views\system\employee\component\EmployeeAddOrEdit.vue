<template>
  <el-dialog v-model="isvisable" draggable :title="editObj.empId ? '编辑' : '新增'" align-center width="520px"
    @close="cancelBtn">
    <el-form ref="dialogRef" :model="dialogueForm" label-width="80px" style="padding:30px" :rules="rules">
      <el-form-item label="工号" prop="username">
        <el-input v-model="dialogueForm.username" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="姓名" prop="nickname">
        <el-input v-model="dialogueForm.nickname" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-radio-group v-model="dialogueForm.sex">
          <el-radio :label="0">男</el-radio>
          <el-radio :label="1">女</el-radio>
          <el-radio :label="2">未知</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="部门" prop="deptId">
        <el-tree-select v-model="dialogueForm.deptId" style="width:100%" :data="deptOptions"
          :props="{ value: 'id', label: 'deptName', children: 'children' }" node-key="id" placeholder="选择部门"
          check-strictly :current-node-key="dialogueForm.deptId" />
      </el-form-item>
      <el-form-item label="岗位" prop="postId">
        <el-select v-model="dialogueForm.postId" filterable placeholder="请选择" style="width: 100%">
          <el-option v-for="item in postList" :key="item.postId" :label="item.postName" :value="item.postId" />
        </el-select>
      </el-form-item>
      <el-form-item label="入职日期" prop="enrollmentDate">
        <el-date-picker format="YYYY-MM-DD" value-format='YYYY-MM-DD' v-model="dialogueForm.enrollmentDate" type="date"
          placeholder="请选择" style="width: 100%" />
      </el-form-item>
      <el-form-item label="电话">
        <el-input v-model="dialogueForm.phoneNumber" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="邮箱">
        <el-input v-model="dialogueForm.email" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="钉钉ID">
        <el-input v-model="dialogueForm.ddId" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="关联账号">
        <el-select v-model="dialogueForm.userId" filterable placeholder="请选择" style="width: 50%">
          <el-option v-for="item in accountList" :key="item.userId" :label="`${item.nickname}-${item.username}`"
            :value="item.userId" />
        </el-select>
        <el-checkbox style="margin-left:20px;" v-model="dialogueForm.related" label="添加账号" size="large" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea"  maxlength="200"   :rows="2" placeholder="请输入" v-model="dialogueForm.remark"></el-input>
      </el-form-item>
      <el-form-item label="图片">
        <ImageUpload :limit="1" :fileSize="100" :isShowTip="false" :fileList="lookImgArr" @onsuccess="successUploadBtn"
          fileWidth="60px" fileHeight="60px"></ImageUpload>
      </el-form-item>

    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelBtn">取消</el-button>
        <el-button type="primary" :loading="loading" @click="confirmBtn">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { addEmployeePageApi, editEmployeePageApi } from '@/api/system/employeeApi'
import { listDept } from '@/api/system/deptApi'
import { listPost } from '@/api/system/postApi'
import { listUserApi } from '@/api/system/user'
const { proxy } = getCurrentInstance();
let props = defineProps({
  isvisable: {
    type: Boolean,
    default: false,
  },
  editObj: {
    type: Object,
    default: () => { },
  }
});

const dialogueForm = ref({ related: false })
const rules = reactive({
  username: [{ required: true, message: '此处不能为空', trigger: 'blur' },],
  nickname: [{ required: true, message: '此处不能为空', trigger: 'blur' }],
  sex: [{ required: true, message: '请选择', trigger: 'blur' }],
  deptId: [{ required: true, message: '请选择', trigger: 'blur' }],
  postId: [{ required: true, message: '请选择', trigger: 'blur' }],
  enrollmentDate: [{ required: true, message: '请选择', trigger: 'blur' }],
})

watch(() => props.isvisable,
  (newNum, oldNum) => {

    if (newNum && Object.keys(props.editObj).length > 0) {
      dialogueForm.value = JSON.parse(JSON.stringify(props.editObj));
      dialogueForm.value.postId = dialogueForm.value.postId ? parseInt(dialogueForm.value.postId) : null
      lookImgArr.value = dialogueForm.value.avatar ? [{ url: import.meta.env.VITE_APP_BASE_API + dialogueForm.value.avatar, path: dialogueForm.value.avatar }] : []
    }
  })

//部门
const deptOptions = ref([]);
let deptList = ref([])
const listDeptFn = async () => {
  let res = await listDept()
  deptList.value = res
  deptOptions.value = proxy.handleTree(res, 'id');
}

//岗位
let postList = ref([])
const listPostFn = async () => {
  let res = await listPost({ status: 1 })
  postList.value = res.rows
}

//账号
let accountList = ref([])
const listUserApiFn = async () => {
  accountList.value = await listUserApi()
}

//图片上传
const lookImgArr = ref([])
const successUploadBtn = (val) => {
  if (val && val.length > 0) {
    dialogueForm.value.avatar = val[0].response.data.fileName
  }
}

const emit = defineEmits(['update:isvisable', 'onSuccess']);
//取消
const dialogRef = ref(null)
const cancelBtn = () => {
  dialogueForm.value = { related: false }
  emit('update:isvisable', false);
  dialogRef.value.resetFields()
}

//确定
let loading = ref(false)
const confirmBtn = async () => {
  let flag = await dialogRef.value.validate()
  if (flag) {
    try {
      loading.value = true
      let postObj = postList.value.find(item => item.postId == dialogueForm.value.postId)
      let postName = postObj ? postObj.postName : ""
      let deptObj = deptList.value.find(item => item.id == dialogueForm.value.deptId)
      let deptName = deptObj ? deptObj.deptName : ""
      if (dialogueForm.value.empId) {
        await editEmployeePageApi({ ...dialogueForm.value, postName, deptName })
        proxy.$modal.msgSuccess(`编辑成功`);
        cancelBtn()
        emit('onSuccess');
      } else {
        await addEmployeePageApi({ ...dialogueForm.value, postName, deptName })
        proxy.$modal.msgSuccess(`新增成功`);
        cancelBtn()
        emit('onSuccess');
      }

    } finally {
      loading.value = false
    }
  }
}


onMounted(() => {
  listDeptFn()
  listPostFn()
  listUserApiFn()
})
</script>

<style lang="scss" ></style>