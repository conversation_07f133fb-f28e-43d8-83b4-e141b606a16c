<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 09:41:22
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 10:01:30
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\DataPage\Ware.vue
 * 
-->
<template>
  <div>
    <div class="top">
      <div class="com-border">
        <div class="title">正面</div>
        <div class="text">
          <div>托盘数： 10</div>
          <div>最大可用高度： 8000 mm</div>
          <div>托盘总重： 1528kg</div>
        </div>
      </div>
      <div class="com-border" style="margin-left: 10px">
        <div class="title">后面</div>
        <div class="text">
          <div>托盘数： 10</div>
          <div>最大可用高度： 8000 mm</div>
          <div>托盘总重： 1528kg</div>
        </div>
      </div>
    </div>

    <div class="bottom">
      <div>总托盘数： 10</div>
      <div>最大可用高度： 8000 mm</div>
      <div>托盘总重： 1528kg</div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.top {
  display: flex;

  .com-border {
    width: 300px;
    height: 300px;
    border: 1px solid #c1c1c1;

    .title {
      height: 40px;
      line-height: 40px;
      background-color: #fafafa;
      border-bottom: 1px solid #c1c1c1;
      padding-left: 20px;
      font-weight: bold;
    }

    .text {
      padding-left: 20px;

      div {
        margin-top: 20px;
      }
    }
  }
}

.bottom {
  margin: 80px 250px;
  div {
    margin-top: 20px;
  }
}
</style>
