<template>
  <div class="oc">
    <el-card class="card">
      <div class="action">
        <div>
          <el-button type="primary" v-hasPermission="['system:worker:add']"
            @click="dialogFlag = true; editObj = {}">添加员工</el-button>
          <el-button :disabled="multiple" v-hasPermission="['system:worker:remove']"
            @click="deleteTableBtn">删除员工</el-button>
        </div>
        <div class="selection">
          <label>状态:</label>
          <el-select v-model="searchForms.status" @change="theResetPageFn" placeholder="状态" style="width: 180px">
            <el-option label="全部" value="全部" />
            <el-option label="正常" :value="1" />
            <el-option label="停用" :value="0" />
          </el-select>
          <SearchList v-model="searchForms.keywords" @update:modelValue="theResetPageFn"></SearchList>
        </div>
      </div>

      <el-table :data="tableList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="60" align="center"></el-table-column>
        <el-table-column label="姓名" prop="nickname" align="center"></el-table-column>
        <el-table-column label="工号" prop="username" align="center"></el-table-column>
        <el-table-column label="性别" prop="sex" align="center"></el-table-column>
        <el-table-column label="照片" width="100" align="center">
          <template #default="scope">
            <img v-if="scope.row.avatar" style="width:100px;height:100px;cursor: pointer;"
              @click="downImgBtn(proxy.configs.getImageUrl(scope.row.avatar))"
              :src="proxy.configs.getImageUrl(scope.row.avatar)" alt="图片消失了">
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column label="岗位" prop="postName" align="center">
        </el-table-column>
        <el-table-column label="电话" prop="phoneNumber" align="center">
        </el-table-column>
        <el-table-column label="邮箱" prop="email" align="center">
        </el-table-column>
        <el-table-column label="钉钉ID" prop="ddId" align="center"></el-table-column>
        <el-table-column label="入职日期" prop="enrollmentDate" align="center"></el-table-column>
        <el-table-column label="所属部门" prop="deptName" align="center"></el-table-column>
        <el-table-column label="状态" prop="status" align="center">
          <template #default="scope">
            <span :style="{ color: scope.row.status === 0 ? '#fc6302' : '#008ca1' }">● </span>
            <span>{{ proxy.getDictLbelFn('sys_status', scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" align="center"></el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <div class="div-flex">
              <el-button class="table-btn" text @click="editTableBtn(scope.row)"
                v-hasPermission="['system:worker:edit']">编辑</el-button>
              <el-button class="table-btn" v-hasPermission="['system:worker:stop']" text
                @click="startOrStopBtn(scope.row)">{{ scope.row.status == 1 ? "停用" : "启用"
                }}</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-row justify="end" style="margin-top: 10px;">
        <el-pagination v-show="thePage.total > 0" background :total="thePage.total" v-model:current-page="thePage.current"
          v-model:page-size="thePage.size" @current-change="thePageFn" layout="total,prev, pager, next">
        </el-pagination>
      </el-row>
    </el-card>

    <EmployeeAddOrEdit v-model:isvisable="dialogFlag" :editObj="editObj" @onSuccess="theResetPageFn">
    </EmployeeAddOrEdit>
  </div>
</template>

<script setup name="Employee">
import { getEmployeePageApi, adjustEmployeeStatusApi, deleteEmployeePageApi } from '@/api/system/employeeApi'
const EmployeeAddOrEdit = defineAsyncComponent(() => import('./component/EmployeeAddOrEdit'))
const { proxy } = getCurrentInstance();

const downImgBtn = (url) => {
  window.open(url)
}

//分页
const thePage = ref({
  current: 1,
  size: proxy.configs.pageSize,
  total: 0
})
const tableList = ref([])
const thePageFn = async () => {
  let obj = {
    pageNum: thePage.value.current,
    pageSize: thePage.value.size,
    status: searchForms.value.status === "全部" ? "" : searchForms.value.status,
    keywords: searchForms.value.keywords
  }
  let res = await getEmployeePageApi(obj)
  tableList.value = res.rows
  thePage.value.total = res.total
}

//搜索
const searchForms = ref({
  status: "全部",
  keywords: ""
})
const theResetPageFn = () => {
  thePage.value.current = 1
  thePageFn()
}

//编辑
const editObj = ref({})
const editTableBtn = (row) => {
  editObj.value = row
  dialogFlag.value = true
}

//删除
const multiple = ref(true);
const deleteIds = ref([])
const handleSelectionChange = (val) => {
  deleteIds.value = val.map(item => item.empId)
  multiple.value = val.length ? false : true
}
const deleteTableBtn = () => {
  proxy.$modal
    .confirm(`是否确认删除选中的的数据项？`)
    .then(() => deleteEmployeePageApi({ ids: deleteIds.value }))
    .then(() => {
      proxy.$modal.msgSuccess(`删除成功`);
      thePageFn()
    })
    .catch(() => {
    });
}

//弹框
let dialogFlag = ref(false)


//启用停用
const startOrStopBtn = async (row) => {
  proxy.$modal
    .confirm(`是否确认${row.status === 1 ? "停用" : "启用"}？`)
    .then(() => adjustEmployeeStatusApi({ empId: row.empId, status: row.status == 1 ? 0 : 1 }))
    .then(() => {
      thePageFn()
      proxy.$modal.msgSuccess(`操作成功`);
    })
    .catch(() => {
    });
}

onActivated(() => {
  thePageFn()
})

</script>

<style lang="scss" scoped></style>