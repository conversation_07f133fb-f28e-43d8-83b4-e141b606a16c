<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 13:32:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 16:22:41
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\TrayPage\modal\ListMOdal.vue
 * 
-->
<template>
  <div>
    <el-dialog
      :title="addForm.trayId !== undefined ? '编辑托盘' : '新增托盘'"
      v-model="dialogueFlag"
      width="520px"
      append-to-body
    >
      <div style="padding: 30px">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="80px">
          <el-form-item label="出入口" prop="gateCode">
            <el-select
              v-model="addForm.gateCode"
              placeholder="请选择"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in OutInList"
                :key="item.cabinetGateId"
                :label="item.gateCode"
                :value="item.gateCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="托盘号" v-if="addForm.trayId">
            <el-input v-model="addForm.trayCode" disabled />
          </el-form-item>
          <el-form-item label="托盘名称" prop="trayName">
            <el-input v-model="addForm.trayName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="最大载重" prop="maxLoad">
            <el-input-number
              v-model="addForm.maxLoad"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="倾斜角度" prop="tiltAngle">
            <el-input-number
              v-model="addForm.tiltAngle"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
        <div class="attentaion">
          <h3>请注意！</h3>
          <div>添加托盘请将新托盘放置到对应的出入口，然后输入相关信息后，点击确定。</div>
          <div>托盘号由系统自动分配生成。</div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading">
            确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getOutInApi } from "@/api/loginApi";
import { addTrayPageApi, editTrayPageApi } from "@/api/home/<USER>";

import { useStore } from "vuex";
import useWebSocket from "@/utils/useWebSocket";
const store = useStore();

const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  gateCode: [{ required: true, message: "请选择", trigger: "blur" }],
  trayName: [{ required: true, message: "请输入", trigger: "blur" }],
  maxLoad: [{ required: true, message: "请输入", trigger: "blur" }],
  tiltAngle: [{ required: true, message: "请输入", trigger: "blur" }],
});

//出入口
let OutInList = ref([]);
const getOutInApiFn = async (id) => {
  OutInList.value = await getOutInApi({
    cabinetId: localStorage.getItem("huogui.id"),
  });
};

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  getOutInApiFn();
  proxy.resetForm("ruleFormRef");
}

//获取基本信息
const connectWebsocketFn = (code) => {
  const { connect, disconnect } = useWebSocket();

  const buildParams = (wcsTaskCode) => {
    return () => ({
      userId: store.state.user.userinfo.userId,
      cabinetId: localStorage.getItem("huogui.id"),
      gateCode: localStorage.getItem("outin.code"),
      type: 1,
      trayInitQueryCommand: {
        wcsTaskCode,
      },
    });
  };

  const handleMessage = (res) => {
    if (res.data.trayInitResultStatus == 1 || res.data.trayInitResultStatus == 2) {
      proxy.openFullLoading();
    } else if (res.data.trayInitResultStatus == 3) {
      proxy.$modal.msgError("操作失败");
      disconnect();
      proxy.openFullLoading().close();
    } else if (res.data.trayInitResultStatus == 4) {
      proxy.$modal.msgSuccess("操作成功");
      proxy.openFullLoading().close();
      dialogueFlag.value = false;
      disconnect();
      emit("on-success");
    } else {
      disconnect();
      proxy.openFullLoading().close();
    }
  };

  connect(import.meta.env.VITE_APP_WEBSOCKET, buildParams(code), handleMessage);
};

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      if (addForm.value.trayId) {
        requestObj = editTrayPageApi({
          ...addForm.value,
          cabinetId: +localStorage.getItem("huogui.id"),
        });
      } else {
        requestObj = addTrayPageApi({
          ...addForm.value,
          cabinetId: +localStorage.getItem("huogui.id"),
        });
      }
      requestObj
        .then((res) => {
          if (!addForm.value.trayId) {
            connectWebsocketFn(res.wcsTaskCode);
          } else {
            dialogueFlag.value = false;
            emit("on-success");
          }
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped>
.attentaion {
  padding: 0 20px;
  width: 100%;
  height: 140px;
  border: 1px solid #faad14;
  border-radius: 4px;
  background-color: #fffbe6;
  div {
    color: #8d8b7f;
  }
}
</style>
