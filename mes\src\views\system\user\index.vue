<!--
 * @Author: 方志良 
 * @Date: 2024-03-29 17:18:13
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-07-26 13:32:18
 * @FilePath: \biweiman-mes-wms-front-end\src\views\system\user\index.vue
 * 
-->
<template>
  <div class="oc">
    <el-card class="card">
      <div class="action">
        <div>
          <el-button type="primary" v-hasPermission="['system:user:add']"
            @click="dialogFlag = true; editObj = {}">添加账号</el-button>
          <el-button :disabled="multiple" v-hasPermission="['system:user:remove']" @click="deleteTableBtn">删除账号
          </el-button>
        </div>
        <div class="selection">
          <label>状态:</label>
          <el-select v-model="searchForms.status" @change="theResetPageFn" placeholder="用户状态" style="width: 180px">
            <el-option label="全部" value="全部" />
            <el-option label="正常" :value="1" />
            <el-option label="停用" :value="0" />
          </el-select>
          <!-- <label>姓名:</label>
          <el-input v-model="searchForms.input" class="action-input" style="width: 180px" placeholder="Please input" /> -->
          <SearchList v-model="searchForms.keywords" @update:modelValue="theResetPageFn"></SearchList>
        </div>
      </div>

      <el-table :data="tableList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="60" align="center"></el-table-column>
        <el-table-column label="姓名" prop="nickname" align="center"></el-table-column>
        <el-table-column label="工号" prop="username" align="center"></el-table-column>
        <el-table-column label="角色" prop="postName" align="center">
          <template #default="scope">
            <span>{{ roleArrFn(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" align="center">
          <template #default="scope">
            <span :style="{ color: scope.row.status == 1 ? '#008ca1' : '#fc6302' }">● </span>
            <span>{{ proxy.getDictLbelFn('sys_status', scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <div class="div-flex">
              <el-button class="table-btn" text v-hasPermission="['system:user:edit']"
                @click="editTableBtn(scope.row)">编辑</el-button>
              <el-button class="table-btn" text v-hasPermission="['system:user:stop']"
                @click="startOrStopBtn(scope.row)">{{ scope.row.status == 1 ? "停用" : "启用"
                }}</el-button>
              <el-button class="table-btn" v-hasPermission="['system:user:resetPwd']" text
                @click="passWordBtn(scope.row)">重置密码</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-row justify="end" style="margin-top: 10px;">
        <el-pagination v-show="thePage.total > 0" background :total="thePage.total" v-model:current-page="thePage.current"
          v-model:page-size="thePage.size" @current-change="thePageFn" layout="total,prev, pager, next">
        </el-pagination>
      </el-row>
    </el-card>

    <UserAddOrEdit v-model:isvisable="dialogFlag" :editObj="editObj" @onSuccess="theResetPageFn"></UserAddOrEdit>
    <UserPassWord v-model:isvisable="passwordFlag" :userId="userId" @onSuccess="theResetPageFn"></UserPassWord>
  </div>
</template>

<script setup name="User">
import { getAccountPageApi, editSwitchStatusApi, deleteAccountPageApi } from '@/api/system/user'
const UserAddOrEdit = defineAsyncComponent(() => import('./component/UserAddOrEdit.vue'));
const UserPassWord = defineAsyncComponent(() => import('./component/UserPassWord'));
const { proxy } = getCurrentInstance();

//分页
const thePage = ref({
  current: 1,
  size: proxy.configs.pageSize,
  total: 0
})
const tableList = ref([])
const thePageFn = async () => {
  let obj = {
    pageNum: thePage.value.current,
    pageSize: thePage.value.size,
    keywords: searchForms.value.keywords,
    status: searchForms.value.status === '全部' ? "" : searchForms.value.status
  }
  let res = await getAccountPageApi(obj)
  tableList.value = res.rows
  thePage.value.total = res.total
}

//搜索
const searchForms = ref({
  status: "全部",
  keywords: ""
})
const theResetPageFn = () => {
  thePage.value.current = 1
  thePageFn()
}

//编辑
const editObj = ref({})
const editTableBtn = (row) => {
  editObj.value = row
  dialogFlag.value = true
}

//删除
const multiple = ref(true);
const deleteIds = ref([])
const handleSelectionChange = (val) => {
  deleteIds.value = val.map(item => item.userId)
  multiple.value = val.length ? false : true
}
const deleteTableBtn = () => {
  proxy.$modal
    .confirm(`是否确认删除选中的的数据项？`)
    .then(() => deleteAccountPageApi({ ids: deleteIds.value }))
    .then(() => {
      proxy.$modal.msgSuccess(`删除成功`);
      thePageFn()
    })
    .catch(() => {
    });
}

//弹框
let dialogFlag = ref(false)

//角色
const roleArrFn = (row) => {
  let arr = []
  if (row.roleDTOList && row.roleDTOList.length > 0) {
    arr = row.roleDTOList.map(item => item.roleName)
  }
  return arr.join('、')
}

//启用停用
const startOrStopBtn = async (row) => {
  proxy.$modal
    .confirm(`是否确认${row.status == 1 ? "停用" : "启用"}此账号？`)
    .then(() => editSwitchStatusApi({ userId: row.userId, status: row.status == 1 ? 0 : 1 }))
    .then(() => {
      thePageFn()
      proxy.$modal.msgSuccess(`操作成功`);
    })
    .catch(() => {
    });
}
//密码
let userId = ref(0)
let passwordFlag = ref(false)
const passWordBtn = (row) => {
  userId.value = row.userId
  passwordFlag.value = true
}

onActivated(() => {
  thePageFn()
})
</script>

<style lang="scss" scoped></style>