<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 14:56:04
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-01 15:03:37
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\SettingsPage\Modal\EeuipModal.vue
 * 
-->
<template>
  <div>
    <el-dialog title="设备信息" v-model="dialogueFlag" width="520px" append-to-body>
      <div style="padding: 30px;">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="80px">
          <el-form-item label="客户名称" prop="lineName">
            <el-input v-model="addForm.lineName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="设备型号" prop="lineCode">
            <el-input v-model="addForm.lineCode" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="电流(Hz)" prop="postSort">
            <el-input-number
              v-model="addForm.postSort"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  lineName: [{ required: true, message: "请输入", trigger: "blur" }],
  lineCode: [{ required: true, message: "请输入", trigger: "blur" }],
});

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      // if (addForm.value.lineId) {
      //   requestObj = editLinePageApi(addForm.value);
      // } else {
      //   requestObj = addLinePageApi(addForm.value);
      // }
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess(addForm.value.id ? "编辑成功" : "新增成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
