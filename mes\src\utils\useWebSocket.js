
import { ref, onUnmounted } from 'vue';

export default function useWebSocket() {
  const socket = ref(null);
  const isConnected = ref(false);
  const messageData = ref(null);
  const error = ref(null);

  /**
   * 连接WebSocket并发送消息
   * @param {string} url WebSocket地址
   * @param {function} buildParams 构建参数的回调函数
   * @param {function} handleMessage 处理消息的回调函数
   * @param {object} options 配置选项
   */
  const connect = (url, buildParams, handleMessage, options = {}) => {
    // 关闭现有连接
    if (socket.value && socket.value.readyState !== WebSocket.CLOSED) {
      disconnect();
    }

    // 创建新连接
    socket.value = new WebSocket(url || import.meta.env.VITE_APP_WEBSOCKET);
    isConnected.value = false;
    error.value = null;

    // 连接成功
    socket.value.onopen = (event) => {
      isConnected.value = true;
      console.log("WebSocket连接成功:", event);

      if (typeof buildParams === 'function') {
        const params = buildParams();
        if (params && socket.value?.readyState === WebSocket.OPEN) {
          socket.value.send(JSON.stringify(params));
        }
      }
    };

    // 接收消息
    socket.value.onmessage = (event) => {
      try {
        const res = JSON.parse(event.data);
        messageData.value = res;

        // 如果有自定义消息处理器
        if (typeof handleMessage === 'function') {
          handleMessage(res);
        }
      } catch (e) {
        error.value = e;
        console.error("消息解析错误:", e);
      }
    };

    // 错误处理
    socket.value.onerror = (err) => {
      error.value = err;
      console.error("WebSocket错误:", err);
      if (options.onError) {
        options.onError(err);
      }
    };

    // 连接关闭
    socket.value.onclose = (event) => {
      isConnected.value = false;
      console.log("连接已关闭:", event);
      if (options.onClose) {
        options.onClose(event);
      }
    };
  };

  // 发送消息
  const send = (data) => {
    if (socket.value?.readyState === WebSocket.OPEN) {
      socket.value.send(JSON.stringify(data));
    } else {
      console.error("WebSocket连接未就绪");
    }
  };

  // 关闭连接
  const disconnect = () => {
    if (socket.value && socket.value.readyState !== WebSocket.CLOSED) {
      console.log('连接已关闭')
      socket.value.close();
    }
  };

  // 组件卸载时自动关闭连接
  onUnmounted(() => {
    disconnect();
  });

  return {
    connect,
    send,
    disconnect,
    isConnected,
    messageData,
    error,
    socket
  };
}