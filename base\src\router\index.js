/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-27 09:48:56
 * @FilePath: \aosikai-cabinet-wms-web\base\src\router\index.js
 * 
 */
/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-26 17:08:22
 * @FilePath: \aosikai-cabinet-wms-web\base\src\router\index.js
 * 
 */
/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-26 17:07:53
 * @FilePath: \aosikai-cabinet-wms-web\base\src\router\index.js
 * 
 */
/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-26 17:07:30
 * @FilePath: \aosikai-cabinet-wms-web\base\src\router\index.js
 * 
 */
/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-26 17:07:13
 * @FilePath: \aosikai-cabinet-wms-web\base\src\router\index.js
 * 
 */
/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-26 17:06:11
 * @FilePath: \aosikai-cabinet-wms-web\base\src\router\index.js
 * 
 */
/*
 * @Author: 方志良 
 * @Date: 2025-06-26 09:40:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-26 13:31:59
 * @FilePath: \aosikai-cabinet-wms-web\base\src\router\index.js
 * 
 */
import { createWebHistory, createRouter } from 'vue-router';
import Layout from '@/layout';

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    hidden: true,
    redirect: '/HomePage',
  },
  {
    path: '/HomePage',
    component: () => import('@/views/HomePage.vue'),
    meta: { requiresAuth: true },
    hidden: true,
    redirect: '/HomePage/main',
    children: [
      {
        path: 'main',
        name: 'MainPage',
        component: () => import('@/views/home/<USER>/index.vue'),
        meta: { title: '主页面', icon: 'home' }
      },
      {
        path: 'alarm',
        name: 'AlarmPage',
        component: () => import('@/views/home/<USER>/index.vue'),
        meta: { title: '设备报警', icon: 'warning' }
      },
      {
        path: 'manual',
        name: 'ManualPage',
        component: () => import('@/views/home/<USER>/index.vue'),
        meta: { title: '手动模式', icon: 'setting' }
      },
      {
        path: 'data',
        name: 'DataPage',
        component: () => import('@/views/home/<USER>/index.vue'),
        meta: { title: '设备数据', icon: 'data' }
      },
      {
        path: 'tray',
        name: 'TrayPage',
        component: () => import('@/views/home/<USER>/index.vue'),
        meta: { title: '托盘管理', icon: 'box' }
      },
      {
        path: 'settings',
        name: 'SettingsPage',
        component: () => import('@/views/home/<USER>/index.vue'),
        meta: { title: '设置', icon: 'setting' }
      }
    ]
  },
  {
    path: '/ExitPage',
    hidden: true,
    component: () => import('@/views/ExitPage.vue'),
  },
  {
    path: '/index',
    component: Layout,
    redirect: '/index/index',
    children: [
      {
        path: '',
        component: () => import('@/views/index.vue'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    }
    return { top: 0 };
  }
});

export default router;
