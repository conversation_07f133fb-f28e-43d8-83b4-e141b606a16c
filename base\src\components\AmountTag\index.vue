<!--
 * @Author: 方志良 
 * @Date: 2024-05-11 14:50:02
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-05-11 16:53:57
 * @FilePath: \biweiman-mes-wms-front-end\src\components\AmountTag\index.vue
 * 
-->
<template>
  <div>
    <div class="tag">
      <el-icon :size="20" class="icon">
        <Money />
      </el-icon>
      <span v-for="item in list" style="margin-left: 10px;" :key="item.title">
        {{ item.title }}
        <span class="weight">{{ item.money }}</span> ;
      </span>
    </div>
  </div>
</template>

<script setup>
import { Money } from '@element-plus/icons-vue';
let props = defineProps({
  list: {
    type: Array,
    default: () => [],
  }
});
</script>

<style lang="scss" scoped>
.tag {
  width: 100%;
  height: 35px;
  background-color: #e6f7ff;
  line-height: 35px;
  margin-bottom: 10px;
  border-radius: 6px;
  border: 1px solid #91d5ff;
}

.icon {
  vertical-align: middle;
  margin-left: 20px;
  color: #108ee9;
}

.weight {
  font-weight: 700;
}
</style>