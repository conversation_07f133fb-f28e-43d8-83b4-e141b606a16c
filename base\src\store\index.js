
/*
 * @Author: 方志良 
 * @Date: 2024-04-08 15:53:26
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-26 15:34:55
 * @FilePath: \aosikai-cabinet-wms-web\base\src\store\index.js
 * 
 */
import { createStore } from 'vuex';
import app from './modules/app';
import user from './modules/user';
import tagsView from './modules/tagsView';
import permission from './modules/permission';
import settings from './modules/settings';
import getters from './getters';
const store = createStore({
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
  },
  getters,
});

export default store;
