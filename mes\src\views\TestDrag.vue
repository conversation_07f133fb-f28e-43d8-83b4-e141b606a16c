<template>
  <div class="app-coniner">
    <div class="container" ref="containerRef">
      <!-- 托盘宽度标签 -->
      <div v-if="selectedTray" class="width-label">宽度: {{ selectedTray.width }}mm</div>

      <!-- 托盘高度标签 -->
      <div v-if="selectedTray" class="height-label">
        高度: {{ selectedTray.height }}mm
      </div>

      <!-- 托盘 -->
      <div
        v-if="selectedTray"
        class="tray"
        :style="{
          width: '1200px',
          height: '600px',
        }"
      >
        <!-- 刻度线 -->
        <div class="scale-lines">
          <!-- 垂直刻度线 -->
          <div
            v-for="i in Math.floor(selectedTray.width / 50)"
            :key="'v-' + i"
            class="scale-line vertical"
            :style="{ left: i * 50 * scaleX + 'px' }"
          ></div>
          <!-- 水平刻度线 -->
          <div
            v-for="i in Math.floor(selectedTray.height / 50)"
            :key="'h-' + i"
            class="scale-line horizontal"
            :style="{ top: i * 50 * scaleY + 'px' }"
          ></div>
        </div>

        <!-- 箱子 -->
        <div
          v-for="(box, index) in placedBoxes"
          :key="index"
          class="box"
          :class="{
            selected: selectedBoxIndex === index,
            dragging: isDragging && dragBoxIndex === index
          }"
          :style="{
            left: box.x * scaleX + 'px',
            bottom: box.y * scaleY + 'px',
            width: box.width * scaleX + 'px',
            height: box.height * scaleY + 'px',
            zIndex: isDragging && dragBoxIndex === index ? 1000 : 1
          }"
          @click="selectBox(index)"
          @mousedown="startLongPress($event, index)"
          @touchstart="startLongPress($event, index)"
        >
          {{ box.type }}
        </div>
      </div>
    </div>

    <el-button
      type="primary"
      @click="adjustBtn"
      style="margin: 20px"
      :disabled="selectedBoxIndex === -1"
    >
      排列方式
    </el-button>

    <el-button type="danger" style="margin: 20px" @click="clearOneBtn" :disabled="selectedBoxIndex === -1">删除</el-button>
    <el-button type="warning" style="margin: 20px" @click="clearAllBtn">清空</el-button>
    <el-button type="success" style="margin: 20px" @click="saveBtn">保存</el-button>

    <div class="drag-tip">
      💡 提示：长按箱子2秒可开启拖拽模式
    </div>

    <el-form label-width="100px" :model="addForm">
      <el-form-item label="托盘" prop="lineId">
        <el-select
          v-model="addForm.lineId"
          placeholder="请选择"
          clearable
          filterable
          @change="trayChange"
        >
          <el-option
            v-for="item in trayList"
            :key="item.id"
            :label="item.code"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 箱子 -->
    <el-table :data="tableList">
      <el-table-column label="长" prop="width" align="center"></el-table-column>
      <el-table-column label="宽" prop="height" align="center"></el-table-column>
      <el-table-column label="操作" width="160" align="center">
        <template #default="scope">
          <el-button text @click="pullAllBtn(scope.row)">铺满</el-button>
          <el-button text @click="pullOneBtn(scope.row)">单个</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
const addForm = ref({});
const containerRef = ref(null);
const selectedTray = ref(null);
const placedBoxes = ref([]);
const selectedBoxIndex = ref(-1);

// 拖拽相关状态
const isDragging = ref(false);
const dragStartTime = ref(0);
const longPressTimer = ref(null);
const dragOffset = ref({ x: 0, y: 0 });
const dragBoxIndex = ref(-1);

// 计算缩放比例
const scaleX = computed(() => {
  return selectedTray.value ? 1200 / selectedTray.value.width : 1;
});

const scaleY = computed(() => {
  return selectedTray.value ? 600 / selectedTray.value.height : 1;
});

const trayList = ref([
  {
    id: 1,
    code: "CD10001",
    width: 1000,
    height: 800,
  },
  {
    id: 2,
    code: "CD10002",
    width: 500,
    height: 300,
  },
  {
    id: 3,
    code: "CD10003",
    width: 1200,
    height: 800,
  },
  {
    id: 4,
    code: "CD10004",
    width: 1000,
    height: 600,
  },
]);

const tableList = ref([
  {
    type: "LMB1",
    width: 110,
    height: 80,
  },
  {
    type: "LMB2",
    width: 50,
    height: 30,
  },
  {
    type: "LMB3",
    width: 120,
    height: 60,
  },
  {
    type: "LMB4",
    width: 80,
    height: 80,
  },
  {
    type: "LMB5",
    width: 100,
    height: 80,
  },
]);

const trayChange = (val) => {
  selectedTray.value = trayList.value.find((item) => item.id === val);
  selectedBoxIndex.value = -1; // 清空选择的箱子

  // 尝试加载保存的数据
  const savedKey = `tray_layout_${val}`;
  const savedData = localStorage.getItem(savedKey);

  if (savedData) {
    try {
      placedBoxes.value = JSON.parse(savedData);
      ElMessage.success('已加载保存的布局');
    } catch (error) {
      console.error('加载保存数据失败:', error);
      placedBoxes.value = [];
    }
  } else {
    placedBoxes.value = []; // 清空已放置的箱子
  }
};

const pullOneBtn = (row) => {
  if (!selectedTray.value) {
    ElMessage.warning("请先选择托盘");
    return;
  }

  const newBox = {
    type: row.type,
    width: row.width,
    height: row.height,
    x: 0,
    y: 0,
    rotated: false, // 添加旋转状态
  };

  // 计算放置位置
  const position = findAvailablePosition(newBox);
  if (position) {
    newBox.x = position.x;
    newBox.y = position.y;
    placedBoxes.value.push(newBox);
  } else {
    ElMessage.warning("托盘空间不足");
  }
};

const pullAllBtn = (row) => {
  if (!selectedTray.value) {
    ElMessage.warning("请先选择托盘");
    return;
  }

  // 计算能放多少个箱子
  const maxCols = Math.floor(selectedTray.value.width / row.width);
  const maxRows = Math.floor(selectedTray.value.height / row.height);
  const totalBoxes = maxCols * maxRows;

  // 清空当前箱子，重新铺满
  placedBoxes.value = [];
  selectedBoxIndex.value = -1;

  for (let i = 0; i < totalBoxes; i++) {
    const col = i % maxCols;
    const rowIndex = Math.floor(i / maxCols);

    placedBoxes.value.push({
      type: row.type,
      width: row.width,
      height: row.height,
      x: col * row.width,
      y: rowIndex * row.height,
      rotated: false,
    });
  }
};

// 检查两个矩形是否重叠
const isOverlapping = (box1, box2) => {
  return !(
    box1.x + box1.width <= box2.x ||
    box2.x + box2.width <= box1.x ||
    box1.y + box1.height <= box2.y ||
    box2.y + box2.height <= box1.y
  );
};

// 检查位置是否可用
const isPositionAvailable = (newBox, x, y) => {
  const testBox = { ...newBox, x, y };

  // 检查是否超出托盘范围
  if (
    x < 0 ||
    y < 0 ||
    x + newBox.width > selectedTray.value.width ||
    y + newBox.height > selectedTray.value.height
  ) {
    return false;
  }

  // 检查是否与现有箱子重叠
  for (const existingBox of placedBoxes.value) {
    if (isOverlapping(testBox, existingBox)) {
      return false;
    }
  }

  return true;
};

// 寻找可用位置
const findAvailablePosition = (newBox) => {
  // 从左下角开始，逐行扫描
  for (let y = 0; y <= selectedTray.value.height - newBox.height; y += 10) {
    for (let x = 0; x <= selectedTray.value.width - newBox.width; x += 10) {
      if (isPositionAvailable(newBox, x, y)) {
        return { x, y };
      }
    }
  }
  return null;
};

// 选择箱子
const selectBox = (index) => {
  if (!isDragging.value) {
    selectedBoxIndex.value = index;
  }
};

// 开始长按检测
const startLongPress = (event, index) => {
  event.preventDefault();

  // 清除之前的定时器
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
  }

  dragStartTime.value = Date.now();

  // 设置长按定时器（2秒）
  longPressTimer.value = setTimeout(() => {
    startDrag(event, index);
  }, 1000);

  // 添加鼠标抬起和触摸结束事件监听
  const cleanup = () => {
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }
    document.removeEventListener('mouseup', cleanup);
    document.removeEventListener('touchend', cleanup);
  };

  document.addEventListener('mouseup', cleanup);
  document.addEventListener('touchend', cleanup);
};

// 开始拖拽
const startDrag = (event, index) => {
  isDragging.value = true;
  dragBoxIndex.value = index;
  selectedBoxIndex.value = index;

  const box = placedBoxes.value[index];
  const rect = containerRef.value.getBoundingClientRect();

  // 获取鼠标或触摸位置
  const clientX = event.clientX || (event.touches && event.touches[0].clientX);
  const clientY = event.clientY || (event.touches && event.touches[0].clientY);

  // 计算鼠标相对于箱子的偏移
  dragOffset.value = {
    x: clientX - rect.left - (box.x * scaleX.value),
    y: rect.bottom - clientY - (box.y * scaleY.value)
  };

  // 添加拖拽事件监听
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('touchmove', handleDrag);
  document.addEventListener('mouseup', endDrag);
  document.addEventListener('touchend', endDrag);

  ElMessage.info('开始拖拽模式');
};

// 处理拖拽移动
const handleDrag = (event) => {
  if (!isDragging.value || dragBoxIndex.value === -1) return;

  event.preventDefault();

  const rect = containerRef.value.getBoundingClientRect();
  const clientX = event.clientX || (event.touches && event.touches[0].clientX);
  const clientY = event.clientY || (event.touches && event.touches[0].clientY);

  // 计算新位置（实际坐标）
  const newX = (clientX - rect.left - dragOffset.value.x) / scaleX.value;
  const newY = (rect.bottom - clientY - dragOffset.value.y) / scaleY.value;

  const box = placedBoxes.value[dragBoxIndex.value];

  // 边界检查
  const clampedX = Math.max(0, Math.min(newX, selectedTray.value.width - box.width));
  const clampedY = Math.max(0, Math.min(newY, selectedTray.value.height - box.height));

  // 更新箱子位置
  placedBoxes.value[dragBoxIndex.value] = {
    ...box,
    x: clampedX,
    y: clampedY
  };
};

// 结束拖拽
const endDrag = (event) => {
  if (!isDragging.value || dragBoxIndex.value === -1) return;

  event.preventDefault();

  const box = placedBoxes.value[dragBoxIndex.value];

  // 检查最终位置是否与其他箱子重叠
  const otherBoxes = placedBoxes.value.filter((_, index) => index !== dragBoxIndex.value);
  let hasOverlap = false;

  for (const otherBox of otherBoxes) {
    if (isOverlapping(box, otherBox)) {
      hasOverlap = true;
      break;
    }
  }

  if (hasOverlap) {
    // 如果重叠，恢复到拖拽前的位置或找一个可用位置
    const availablePosition = findAvailablePosition(box);
    if (availablePosition) {
      placedBoxes.value[dragBoxIndex.value] = {
        ...box,
        x: availablePosition.x,
        y: availablePosition.y
      };
      ElMessage.warning('位置重叠，已自动调整到可用位置');
    } else {
      ElMessage.error('没有可用位置，拖拽失败');
    }
  } else {
    ElMessage.success('拖拽完成');
  }

  // 清理拖拽状态
  isDragging.value = false;
  dragBoxIndex.value = -1;
  dragOffset.value = { x: 0, y: 0 };

  // 移除事件监听
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('touchmove', handleDrag);
  document.removeEventListener('mouseup', endDrag);
  document.removeEventListener('touchend', endDrag);
};

// 切换箱子排列方式
const adjustBtn = () => {
  if (selectedBoxIndex.value === -1) {
    ElMessage.warning("请先选择一个箱子");
    return;
  }

  const selectedBox = placedBoxes.value[selectedBoxIndex.value];

  // 创建旋转后的箱子
  const rotatedBox = {
    ...selectedBox,
    width: selectedBox.height,
    height: selectedBox.width,
    rotated: !selectedBox.rotated,
  };

  // 临时移除当前箱子，检查旋转后是否能放在当前位置
  const otherBoxes = placedBoxes.value.filter(
    (_, index) => index !== selectedBoxIndex.value
  );

  // 检查旋转后的箱子是否超出托盘范围
  if (
    selectedBox.x + rotatedBox.width > selectedTray.value.width ||
    selectedBox.y + rotatedBox.height > selectedTray.value.height
  ) {
    ElMessage.warning("有箱子挡到了，切换不了排列方式");
    return;
  }

  // 检查旋转后的箱子是否与其他箱子重叠
  const testBox = { ...rotatedBox, x: selectedBox.x, y: selectedBox.y };
  let hasOverlap = false;

  for (const otherBox of otherBoxes) {
    if (isOverlapping(testBox, otherBox)) {
      hasOverlap = true;
      break;
    }
  }

  if (hasOverlap) {
    ElMessage.warning("有箱子挡到了，切换不了排列方式");
  } else {
    // 可以旋转，更新箱子
    placedBoxes.value[selectedBoxIndex.value] = rotatedBox;
  }
};

// 删除选中的箱子
const clearOneBtn = () => {
  if (selectedBoxIndex.value === -1) {
    ElMessage.warning('请先选择一个箱子');
    return;
  }

  // 删除选中的箱子
  placedBoxes.value.splice(selectedBoxIndex.value, 1);
  selectedBoxIndex.value = -1; // 清空选择
  ElMessage.success('箱子已删除');
};

// 清空所有箱子
const clearAllBtn = () => {
  if (placedBoxes.value.length === 0) {
    ElMessage.warning('托盘上没有箱子');
    return;
  }

  placedBoxes.value = [];
  selectedBoxIndex.value = -1;
  ElMessage.success('已清空所有箱子');
};

// 保存当前布局到本地存储
const saveBtn = () => {
  if (!selectedTray.value) {
    ElMessage.warning('请先选择托盘');
    return;
  }

  if (placedBoxes.value.length === 0) {
    ElMessage.warning('托盘上没有箱子，无需保存');
    return;
  }

  const savedKey = `tray_layout_${selectedTray.value.id}`;
  const dataToSave = JSON.stringify(placedBoxes.value);

  try {
    localStorage.setItem(savedKey, dataToSave);
    ElMessage.success(`已保存托盘 ${selectedTray.value.code} 的布局`);
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败，请检查浏览器存储权限');
  }
};
</script>

<style lang="scss" scoped>
.app-coniner {
  padding: 100px;
}

.drag-tip {
  margin: 10px 20px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #409eff;
  border-radius: 4px;
  color: #409eff;
  font-size: 14px;
  display: inline-block;
}

.container {
  width: 1200px;
  height: 600px;
  border: 1px solid #797979;
  position: relative;
  margin-bottom: 20px;
}

.width-label {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.height-label {
  position: absolute;
  left: -80px;
  top: 50%;
  transform: translateY(-50%) rotate(-90deg);
  background-color: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.tray {
  position: absolute;
  bottom: 0;
  left: 0;
  border: 2px solid #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.scale-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.scale-line {
  position: absolute;
  background-color: #ddd;

  &.vertical {
    width: 1px;
    height: 100%;
  }

  &.horizontal {
    width: 100%;
    height: 1px;
  }
}

.box {
  position: absolute;
  border: 1px solid #333;
  background-color: rgba(255, 193, 7, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 193, 7, 1);
    transform: scale(1.02);
  }

  &.selected {
    border: 2px solid #e74c3c;
    background-color: rgba(231, 76, 60, 0.8);
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
  }

  &.dragging {
    opacity: 0.8;
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 2px solid #409eff;
    background-color: rgba(64, 158, 255, 0.9);
    cursor: grabbing;
  }
}
</style>
