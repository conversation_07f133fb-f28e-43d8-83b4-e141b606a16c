<template>
  <div class="app-coniner">
    <div class="container" ref="containerRef">
      <!-- 托盘 -->
      <div 
        v-if="selectedTray" 
        class="tray"
        :style="{
          width: '1200px',
          height: '600px'
        }"
      >
        <!-- 刻度线 -->
        <div class="scale-lines">
          <!-- 垂直刻度线 -->
          <div 
            v-for="i in Math.floor(selectedTray.width / 50)"
            :key="'v-' + i"
            class="scale-line vertical"
            :style="{ left: (i * scaleX) + 'px' }"
          ></div>
          <!-- 水平刻度线 -->
          <div 
            v-for="i in Math.floor(selectedTray.height / 50)"
            :key="'h-' + i"
            class="scale-line horizontal"
            :style="{ top: (i * scaleY) + 'px' }"
          ></div>
        </div>
        
        <!-- 箱子 -->
        <div 
          v-for="(box, index) in placedBoxes"
          :key="index"
          class="box"
          :style="{
            left: (box.x * scaleX) + 'px',
            bottom: (box.y * scaleY) + 'px',
            width: (box.width * scaleX) + 'px',
            height: (box.height * scaleY) + 'px'
          }"
        >
          {{ box.type }}
        </div>
      </div>
    </div>

    <el-button type="primary" @click="adjustBtn" style="margin: 20px">排列方式</el-button>

    <el-form label-width="100px" :model="addForm">
      <el-form-item label="托盘" prop="lineId">
        <el-select
          v-model="addForm.lineId"
          placeholder="请选择"
          clearable
          filterable
          @change="trayChange"
        >
          <el-option
            v-for="item in trayList"
            :key="item.id"
            :label="item.code"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 箱子 -->
    <el-table :data="tableList">
      <el-table-column label="长" prop="width" align="center"></el-table-column>
      <el-table-column label="宽" prop="height" align="center"></el-table-column>
      <el-table-column label="操作" width="160" align="center">
        <template #default="scope">
          <el-button text @click="pullAllBtn(scope.row)">铺满</el-button>
          <el-button text @click="pullOneBtn(scope.row)">单个</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
const addForm = ref({});
const containerRef = ref(null);
const selectedTray = ref(null);
const placedBoxes = ref([]);

// 计算缩放比例
const scaleX = computed(() => {
  return selectedTray.value ? 1200 / selectedTray.value.width : 1;
});

const scaleY = computed(() => {
  return selectedTray.value ? 600 / selectedTray.value.height : 1;
});

const trayList = ref([
  {
    id: 1,
    code: "CD10001",
    width: 1000,
    height: 800,
  },
  {
    id: 2,
    code: "CD10002",
    width: 500,
    height: 300,
  },
  {
    id: 3,
    code: "CD10003",
    width: 1200,
    height: 800,
  },
  {
    id: 4,
    code: "CD10004",
    width: 1000,
    height: 600,
  },
]);

const tableList = ref([
  {
    type: "LMB1",
    width: 110,
    height: 80,
  },
  {
    type: "LMB2",
    width: 50,
    height: 30,
  },
  {
    type: "LMB3",
    width: 120,
    height: 800,
  },
]);

const trayChange = (val) => {
  selectedTray.value = trayList.value.find(item => item.id === val);
  placedBoxes.value = []; // 清空已放置的箱子
};

const pullOneBtn = (row) => {
  if (!selectedTray.value) {
    ElMessage.warning('请先选择托盘');
    return;
  }
  
  const newBox = {
    type: row.type,
    width: row.width,
    height: row.height,
    x: 0,
    y: 0
  };
  
  // 计算放置位置
  const position = calculateNextPosition(newBox);
  if (position) {
    newBox.x = position.x;
    newBox.y = position.y;
    placedBoxes.value.push(newBox);
  } else {
    ElMessage.warning('托盘空间不足');
  }
};

const pullAllBtn = (row) => {
  if (!selectedTray.value) {
    ElMessage.warning('请先选择托盘');
    return;
  }
  
  // 计算能放多少个箱子
  const maxCols = Math.floor(selectedTray.value.width / row.width);
  const maxRows = Math.floor(selectedTray.value.height / row.height);
  const totalBoxes = maxCols * maxRows;
  
  // 清空当前箱子，重新铺满
  placedBoxes.value = [];
  
  for (let i = 0; i < totalBoxes; i++) {
    const col = i % maxCols;
    const rowIndex = Math.floor(i / maxCols);
    
    placedBoxes.value.push({
      type: row.type,
      width: row.width,
      height: row.height,
      x: col * row.width,
      y: rowIndex * row.height
    });
  }
};

const calculateNextPosition = (box) => {
  if (placedBoxes.value.length === 0) {
    return { x: 0, y: 0 };
  }
  
  // 计算每行能放多少个箱子
  const maxCols = Math.floor(selectedTray.value.width / box.width);
  
  // 按行排列，找到下一个可用位置
  let targetRow = 0;
  let targetCol = 0;
  
  // 计算当前应该放在第几行第几列
  const currentBoxCount = placedBoxes.value.length;
  targetRow = Math.floor(currentBoxCount / maxCols);
  targetCol = currentBoxCount % maxCols;
  
  const x = targetCol * box.width;
  const y = targetRow * box.height;
  
  // 检查是否超出托盘范围
  if (x + box.width <= selectedTray.value.width && y + box.height <= selectedTray.value.height) {
    return { x, y };
  }
  
  return null; // 无法放置
};

const adjustBtn = () => {};
</script>

<style lang="scss" scoped>
.app-coniner {
  padding: 100px;
}

.container {
  width: 1200px;
  height: 600px;
  border: 1px solid #797979;
  position: relative;
  margin-bottom: 20px;
}

.tray {
  position: absolute;
  bottom: 0;
  left: 0;
  border: 2px solid #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.scale-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.scale-line {
  position: absolute;
  background-color: #ddd;
  
  &.vertical {
    width: 1px;
    height: 100%;
  }
  
  &.horizontal {
    width: 100%;
    height: 1px;
  }
}

.box {
  position: absolute;
  border: 1px solid #333;
  background-color: rgba(255, 193, 7, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}
</style>
